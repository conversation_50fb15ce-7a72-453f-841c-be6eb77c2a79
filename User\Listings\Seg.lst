C51 COMPILER V9.54   SEG                                                                   02/10/2025 22:15:57 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE SEG
OBJECT MODULE PLACED IN .\Objects\Seg.obj
COMPILER INVOKED BY: E:\keil5\Keil5 C51\C51\BIN\C51.EXE ..\Driver\Seg.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG
                    - OBJECTEXTEND PRINT(.\Listings\Seg.lst) OBJECT(.\Objects\Seg.obj)

line level    source

   1          #include <STC15F2K60S2.H>
   2          #include "Seg.h"
   3          
   4          unsigned char Seg_Dula[] = {0xc0,0xf9,0xa4,0xb0,0x99,0x92,0x82,0xf8,0x80,0x90,0xff,0xc1,0x8e};
   5          unsigned char Seg_Wela[] = {0x01,0x02,0x04,0x08,0x10,0x20,0x40,0x80};
   6          //共阳极接法，0为亮，高位是dp，g，f，反着来的
   7          
   8          void Seg_Disp(unsigned char wela,dula,point)
   9          {
  10   1              P0 = 0xFF;//全灭
  11   1              P2 = P2 & 0x1F | 0xE0;//打开0xE0对应YnC的锁存器
  12   1              P2 = P2 & 0x1F;//关闭锁存器
  13   1              //先段选消隐，后位选再段选
  14   1              P0 = Seg_Wela[wela];
  15   1              P2 = P2 & 0x1F | 0xC0;
  16   1              P2 = P2 & 0x1F;
  17   1              
  18   1              P0 = Seg_Dula[dula];
  19   1              if(point)//如果小数点使能
  20   1                      P0 &= 0x7F;//0111 1111dp位常量
  21   1              P2 = P2 & 0x1F | 0xE0;
  22   1              P2 = P2 & 0x1F;
  23   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =     58    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =     21    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
