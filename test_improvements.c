/*=============================================================================
 * 系统改进测试程序
 * 功能：测试LCD即时响应、时钟显示优化、电机PWM控制
 *============================================================================*/

#include <REG52.H>
#include "Config.h"
#include "Key.h"
#include "Display.h"
#include "Motor.h"
#include "DS1302.h"
#include "UART.h"

/*-----------------------------------------------------------------------------
 * 测试1：LCD即时响应测试
 * 验证按键按下后屏幕是否立即刷新
 *----------------------------------------------------------------------------*/
void Test_LCD_Response(void)
{
    unsigned char key_val;
    unsigned int test_counter = 0;
    
    UART_Send_String("=== LCD即时响应测试 ===\r\n");
    UART_Send_String("请按任意按键测试屏幕响应速度\r\n");
    
    while(test_counter < 100)  // 测试100次按键
    {
        key_val = Key_Read();
        if(key_val)
        {
            // 记录按键按下时间
            UART_Send_String("按键按下，屏幕应立即刷新\r\n");
            
            // 模拟按键处理（实际在Key_Process中）
            g_system.target_temp += 1.0;
            if(g_system.target_temp > TEMP_MAX)
                g_system.target_temp = TEMP_MIN;
                
            // 立即更新显示（这是关键改进）
            Display_Update();
            
            UART_Send_String("显示已更新\r\n");
            test_counter++;
            
            // 等待按键释放
            while(Key_Read());
        }
    }
    
    UART_Send_String("LCD响应测试完成\r\n\r\n");
}

/*-----------------------------------------------------------------------------
 * 测试2：时钟显示优化测试
 * 验证时钟更新频率是否提升至500ms
 *----------------------------------------------------------------------------*/
void Test_Clock_Update(void)
{
    Time_t last_time, current_time;
    unsigned int update_count = 0;
    unsigned int test_duration = 0;
    
    UART_Send_String("=== 时钟显示优化测试 ===\r\n");
    UART_Send_String("测试时钟更新频率（应为500ms）\r\n");
    
    // 获取初始时间
    DS1302_Get_Time(&last_time);
    
    while(test_duration < 10000)  // 测试10秒
    {
        DS1302_Get_Time(&current_time);
        
        // 检查时间是否更新
        if(current_time.second != last_time.second)
        {
            update_count++;
            UART_Send_String("时钟更新: ");
            UART_Send_Num(current_time.hour);
            UART_Send_String(":");
            UART_Send_Num(current_time.minute);
            UART_Send_String(":");
            UART_Send_Num(current_time.second);
            UART_Send_String("\r\n");
            
            last_time = current_time;
        }
        
        test_duration++;
        delay_ms(1);
    }
    
    UART_Send_String("时钟更新测试完成，更新次数: ");
    UART_Send_Num(update_count);
    UART_Send_String("\r\n\r\n");
}

/*-----------------------------------------------------------------------------
 * 测试3：电机PWM控制测试
 * 验证定时器1 PWM信号是否正常工作
 *----------------------------------------------------------------------------*/
void Test_Motor_PWM(void)
{
    unsigned char test_speed;
    unsigned char test_dir;
    
    UART_Send_String("=== 电机PWM控制测试 ===\r\n");
    UART_Send_String("测试电机PWM信号和方向控制\r\n");
    
    // 测试不同转速
    for(test_speed = 0; test_speed <= 255; test_speed += 51)
    {
        UART_Send_String("测试转速: ");
        UART_Send_Num(test_speed);
        UART_Send_String("\r\n");
        
        // 测试正转
        UART_Send_String("正转测试...\r\n");
        Motor_Control(MOTOR_FORWARD, test_speed);
        delay_ms(2000);  // 运行2秒
        
        // 测试反转
        UART_Send_String("反转测试...\r\n");
        Motor_Control(MOTOR_REVERSE, test_speed);
        delay_ms(2000);  // 运行2秒
        
        // 停止
        Motor_Control(MOTOR_STOP, 0);
        delay_ms(1000);  // 停止1秒
    }
    
    UART_Send_String("电机PWM测试完成\r\n\r\n");
}

/*-----------------------------------------------------------------------------
 * 测试4：综合性能测试
 * 同时测试所有改进功能
 *----------------------------------------------------------------------------*/
void Test_Overall_Performance(void)
{
    unsigned int test_cycle = 0;
    
    UART_Send_String("=== 综合性能测试 ===\r\n");
    UART_Send_String("同时测试LCD响应、时钟更新、电机控制\r\n");
    
    while(test_cycle < 50)  // 测试50个周期
    {
        // 模拟按键操作
        g_system.target_temp = 25.0 + (test_cycle % 10);
        Display_Update();  // 立即更新显示
        
        // 读取时钟
        DS1302_Get_Time(&current_time);
        
        // 控制电机
        if(test_cycle % 2 == 0)
            Motor_Control(MOTOR_FORWARD, 100 + (test_cycle % 100));
        else
            Motor_Control(MOTOR_REVERSE, 100 + (test_cycle % 100));
        
        // 输出测试状态
        UART_Send_String("测试周期: ");
        UART_Send_Num(test_cycle);
        UART_Send_String(", 目标温度: ");
        UART_Send_Float(g_system.target_temp);
        UART_Send_String(", 电机状态: ");
        UART_Send_Num(g_system.motor_state);
        UART_Send_String("\r\n");
        
        test_cycle++;
        delay_ms(500);  // 每500ms一个周期
    }
    
    Motor_Control(MOTOR_STOP, 0);  // 停止电机
    UART_Send_String("综合性能测试完成\r\n\r\n");
}

/*-----------------------------------------------------------------------------
 * 主测试函数
 *----------------------------------------------------------------------------*/
void main(void)
{
    // 系统初始化
    Motor_Init();
    Display_Init();
    DS1302_Init();
    UART_Init();
    
    UART_Send_String("\r\n=== 系统改进测试程序 V1.1 ===\r\n");
    UART_Send_String("测试内容：\r\n");
    UART_Send_String("1. LCD即时响应\r\n");
    UART_Send_String("2. 时钟显示优化\r\n");
    UART_Send_String("3. 电机PWM控制\r\n");
    UART_Send_String("4. 综合性能测试\r\n\r\n");
    
    // 依次执行各项测试
    Test_LCD_Response();
    Test_Clock_Update();
    Test_Motor_PWM();
    Test_Overall_Performance();
    
    UART_Send_String("=== 所有测试完成 ===\r\n");
    UART_Send_String("请检查测试结果和硬件响应\r\n");
    
    // 进入正常运行模式
    while(1)
    {
        // 正常系统运行
        delay_ms(1000);
    }
}
