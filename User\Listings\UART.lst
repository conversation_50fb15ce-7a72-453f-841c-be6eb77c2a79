C51 COMPILER V9.54   UART                                                                  07/07/2025 00:36:35 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE UART
OBJECT MODULE PLACED IN .\Objects\UART.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\UART.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTEXT
                    -END PRINT(.\Listings\UART.lst) OBJECT(.\Objects\UART.obj)

line level    source

   1          #include "UART.h"
   2          
   3          /*=============================================================================
   4           * 串口通信模块实现
   5           * 功能：串口初始化、数据发送、系统状态输出
   6           *============================================================================*/
   7          
   8          /*-----------------------------------------------------------------------------
   9           * 函数名称: UART_Init
  10           * 功能描述: 串口初始化，波特率9600
  11           *----------------------------------------------------------------------------*/
  12          void UART_Init(void)
  13          {
  14   1          SCON = 0x50;    // 串口模式1，允许接收
  15   1          TMOD |= 0x20;   // 定时器1模式2，8位自动重装
  16   1          TH1 = 0xFD;     // 波特率9600@11.0592MHz
  17   1          TL1 = 0xFD;
  18   1          TR1 = 1;        // 启动定时器1
  19   1          TI = 1;         // 发送中断标志置位
  20   1      }
  21          
  22          /*-----------------------------------------------------------------------------
  23           * 函数名称: UART_Send_Byte
  24           * 功能描述: 发送单个字节
  25           * 输入参数: dat - 要发送的字节
  26           *----------------------------------------------------------------------------*/
  27          void UART_Send_Byte(unsigned char dat)
  28          {
  29   1          SBUF = dat;     // 发送数据
  30   1          while(!TI);     // 等待发送完成
  31   1          TI = 0;         // 清除发送标志
  32   1      }
  33          
  34          /*-----------------------------------------------------------------------------
  35           * 函数名称: UART_Send_String
  36           * 功能描述: 发送字符串
  37           * 输入参数: str - 字符串指针
  38           *----------------------------------------------------------------------------*/
  39          void UART_Send_String(unsigned char *str)
  40          {
  41   1          while(*str)
  42   1          {
  43   2              UART_Send_Byte(*str);
  44   2              str++;
  45   2          }
  46   1      }
  47          
  48          /*-----------------------------------------------------------------------------
  49           * 函数名称: UART_Send_Float
  50           * 功能描述: 发送浮点数
  51           * 输入参数: value - 浮点数值, precision - 小数位数
  52           *----------------------------------------------------------------------------*/
  53          void UART_Send_Float(float value, unsigned char precision)
  54          {
C51 COMPILER V9.54   UART                                                                  07/07/2025 00:36:35 PAGE 2   

  55   1          int integer_part;
  56   1          float decimal_part;
  57   1          unsigned char i;
  58   1          unsigned char digit;
  59   1          
  60   1          // 处理负数
  61   1          if(value < 0)
  62   1          {
  63   2              UART_Send_Byte('-');
  64   2              value = -value;
  65   2          }
  66   1          
  67   1          // 整数部分
  68   1          integer_part = (int)value;
  69   1          if(integer_part == 0)
  70   1          {
  71   2              UART_Send_Byte('0');
  72   2          }
  73   1          else
  74   1          {
  75   2              // 递归发送整数部分
  76   2              unsigned char digits[10];
  77   2              unsigned char digit_count = 0;
  78   2              
  79   2              while(integer_part > 0)
  80   2              {
  81   3                  digits[digit_count++] = integer_part % 10;
  82   3                  integer_part /= 10;
  83   3              }
  84   2              
  85   2              for(i = digit_count; i > 0; i--)
  86   2              {
  87   3                  UART_Send_Byte('0' + digits[i-1]);
  88   3              }
  89   2          }
  90   1          
  91   1          // 小数点
  92   1          if(precision > 0)
  93   1          {
  94   2              UART_Send_Byte('.');
  95   2              
  96   2              // 小数部分
  97   2              decimal_part = value - (int)value;
  98   2              for(i = 0; i < precision; i++)
  99   2              {
 100   3                  decimal_part *= 10;
 101   3                  digit = (unsigned char)decimal_part;
 102   3                  UART_Send_Byte('0' + digit);
 103   3                  decimal_part -= digit;
 104   3              }
 105   2          }
 106   1      }
 107          
 108          /*-----------------------------------------------------------------------------
 109           * 函数名称: UART_Send_System_Data
 110           * 功能描述: 发送系统数据
 111           *----------------------------------------------------------------------------*/
 112          void UART_Send_System_Data(void)
 113          {
 114   1          // 发送数据格式：TEMP:25.6,MOTOR:1.2V,NTC:45.2,CURRENT:0.8A,TIME:12:30:45
 115   1          
 116   1          UART_Send_String("TEMP:");
C51 COMPILER V9.54   UART                                                                  07/07/2025 00:36:35 PAGE 3   

 117   1          UART_Send_Float(g_system.current_temp, 1);
 118   1          
 119   1          UART_Send_String(",MOTOR:");
 120   1          UART_Send_Float(g_system.motor_voltage, 1);
 121   1          UART_Send_String("V");
 122   1          
 123   1          UART_Send_String(",NTC:");
 124   1          UART_Send_Float(g_system.chip_temp, 1);
 125   1          
 126   1          UART_Send_String(",CURRENT:");
 127   1          UART_Send_Float(g_system.current_detect, 2);
 128   1          UART_Send_String("A");
 129   1          
 130   1          UART_Send_String(",TIME:");
 131   1          if(g_system.hour < 10) UART_Send_Byte('0');
 132   1          UART_Send_Float(g_system.hour, 0);
 133   1          UART_Send_Byte(':');
 134   1          if(g_system.minute < 10) UART_Send_Byte('0');
 135   1          UART_Send_Float(g_system.minute, 0);
 136   1          UART_Send_Byte(':');
 137   1          if(g_system.second < 10) UART_Send_Byte('0');
 138   1          UART_Send_Float(g_system.second, 0);
 139   1          
 140   1          UART_Send_String(",TARGET:");
 141   1          UART_Send_Float(g_system.target_temp, 1);
 142   1          
 143   1          UART_Send_String(",MOTOR_STATE:");
 144   1          UART_Send_Float(g_system.motor_state, 0);
 145   1          
 146   1          UART_Send_String(",MOTOR_SPEED:");
 147   1          UART_Send_Float(g_system.motor_speed, 0);
 148   1          
 149   1          UART_Send_String("\r\n");  // 换行
 150   1      }
 151          
 152          /*-----------------------------------------------------------------------------
 153           * 函数名称: UART_Send_Alarm_Info
 154           * 功能描述: 发送报警信息
 155           *----------------------------------------------------------------------------*/
 156          void UART_Send_Alarm_Info(void)
 157          {
 158   1          float deviation;
 159   1      
 160   1          if(g_system.alarm_level > 0)
 161   1          {
 162   2              UART_Send_String("ALARM:LEVEL");
 163   2              UART_Send_Float(g_system.alarm_level, 0);
 164   2      
 165   2              UART_Send_String(",DEVIATION:");
 166   2              deviation = g_system.current_temp - g_system.target_temp;
 167   2              if(deviation < 0) deviation = -deviation;
 168   2              UART_Send_Float(deviation, 1);
 169   2      
 170   2              UART_Send_String("\r\n");
 171   2          }
 172   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    725    ----
   CONSTANT SIZE    =    105    ----
   XDATA SIZE       =   ----    ----
C51 COMPILER V9.54   UART                                                                  07/07/2025 00:36:35 PAGE 4   

   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----      26
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
