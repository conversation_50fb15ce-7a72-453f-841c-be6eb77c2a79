C51 COMPILER V9.54   LED                                                                   02/11/2025 12:05:08 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE LED
OBJECT MODULE PLACED IN .\Objects\Led.obj
COMPILER INVOKED BY: E:\keil5\Keil5 C51\C51\BIN\C51.EXE ..\Driver\Led.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG
                    - OBJECTEXTEND PRINT(.\Listings\Led.lst) OBJECT(.\Objects\Led.obj)

line level    source

   1          #include <STC15F2K60S2.H>
   2          #include "Led.h"
   3          
   4          void Led_Disp(unsigned char addr,enable)
   5          {
   6   1              static unsigned char temp = 0x00;
   7   1              static unsigned char temp_old = 0xFF;
   8   1      
   9   1              if(enable)
  10   1                      temp = temp | 0x01 <<addr;//选中某位赋值1
  11   1              else
  12   1                      temp &= ~(0x01 << addr);//否则取反，与关系
  13   1              if(temp != temp_old)//判断是否进行了一次数据刷新
  14   1              {
  15   2                      P0 = ~temp;//取反赋值，准备点亮LED
  16   2                      P2 = P2 & 0x1F | 0x80;//LED对应Y4C使能，让LED亮起来
  17   2                      P2 = P2 & 0x1F;
  18   2                      
  19   2                      temp_old = temp;//记录当前的temp值
  20   2              }
  21   1      }
  22          
  23          void Beep(unsigned char flag)
  24          {
  25   1              static unsigned char temp = 0x00;
  26   1              static unsigned char temp_old = 0xff;
  27   1              if(flag)
  28   1                      temp |= 0x40;
  29   1              else
  30   1                      temp &= ~0x40;
  31   1              if(temp != temp_old)
  32   1              {
  33   2                      P0 = temp;
  34   2                      P2 = P2 & 0x1f | 0xa0;
  35   2                      P2 &= 0x1f;
  36   2                      temp_old = temp;                
  37   2              }
  38   1      }
  39          
  40          void Relay(unsigned char flag)
  41          {
  42   1              static unsigned char temp = 0x00;
  43   1              static unsigned char temp_old = 0xff;
  44   1              if(flag)
  45   1                      temp |= 0x10;
  46   1              else
  47   1                      temp &= ~0x10;
  48   1              if(temp != temp_old)
  49   1              {
  50   2                      P0 = temp;
  51   2                      P2 = P2 & 0x1f | 0xa0;
  52   2                      P2 &= 0x1f;
  53   2                      temp_old = temp;                
  54   2              }       
C51 COMPILER V9.54   LED                                                                   02/11/2025 12:05:08 PAGE 2   

  55   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    129    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =      6    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
