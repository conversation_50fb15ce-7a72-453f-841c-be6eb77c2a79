C51 COMPILER V9.54   MAIN                                                                  07/07/2025 09:58:07 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE MAIN
OBJECT MODULE PLACED IN .\Objects\main.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE main.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTEXTEND PRINT(
                    -.\Listings\main.lst) OBJECT(.\Objects\main.obj)

line level    source

   1          /*=============================================================================
   2           * 智能温控保温杯系统主程序
   3           * 功能：电机温控、多传感器检测、LCD显示、渐变报警、串口通信
   4           *============================================================================*/
   5          #define MAIN_C  // 定义全局变量
   6          
   7          /*头文件声明区*/
   8          #include <REG52.H>     // 单片机寄存器专用头文件
   9          #include "Config.h"    // 系统配置文件
  10          #include "Key.h"       // 按键驱动
  11          #include "Delay.h"     // 延时函数
  12          #include "LCD9648.h"   // LCD显示
  13          #include "iic.h"       // IIC通信
  14          #include "onewire.h"   // DS18B20温度传感器
  15          #include "Motor.h"     // 电机控制
  16          #include "Sensor.h"    // 传感器检测
  17          #include "DS1302.h"    // 时钟模块
  18          #include "Alarm.h"     // 报警模块
  19          #include "Display.h"   // 显示扩展
  20          #include "UART.h"      // 串口通信
  21          #include "ADC.h"       // ADC芯片驱动
  22          /*变量声明区*/
  23          unsigned char Key_Val, Key_Up, Key_Down, Key_Old;  // 按键相关变量
  24          unsigned char Key_Slow_Down;
  25          
  26          // 任务调度计数器
  27          unsigned int task_1ms_counter = 0;    // 1ms任务计数器
  28          unsigned int task_10ms_counter = 0;   // 10ms任务计数器
  29          unsigned int task_100ms_counter = 0;  // 100ms任务计数器
  30          unsigned int task_500ms_counter = 0;  // 500ms任务计数器
  31          unsigned int task_1000ms_counter = 0; // 1000ms任务计数器
  32          
  33          // 时钟相关变量
  34          Time_t current_time;
  35          unsigned int software_timer_counter = 0;  // 软件计时器计数器
  36          
  37          
  38          /*-----------------------------------------------------------------------------
  39           * 函数名称: Key_Process
  40           * 功能描述: 按键处理函数，支持温度设定和菜单操作
  41           *----------------------------------------------------------------------------*/
  42          void Key_Process(void)
  43          {
  44   1              unsigned char need_update = 0;  // 标记是否需要立即更新显示
  45   1      
  46   1              if(Key_Slow_Down) return;
  47   1              Key_Slow_Down = 1;
  48   1      
  49   1              Key_Val = Key_Read();
  50   1              Key_Down = Key_Val & (Key_Old ^ Key_Val);
  51   1              Key_Up = ~Key_Val & (Key_Old ^ Key_Val);
  52   1              Key_Old = Key_Val;
  53   1      
  54   1              // 按键处理
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 09:58:07 PAGE 2   

  55   1              switch(Key_Down)
  56   1              {
  57   2                      case 1:  // KEY1 - 温度设定+ / 数值增加
  58   2                              if(g_system.system_mode == MODE_SETTING)
  59   2                              {
  60   3                                      if(g_system.menu_index == MENU_TEMP_SET)
  61   3                                      {
  62   4                                              g_system.target_temp += 1.0;
  63   4                                              if(g_system.target_temp > TEMP_MAX)
  64   4                                                      g_system.target_temp = TEMP_MAX;
  65   4                                              need_update = 1;  // 需要立即更新显示
  66   4                                      }
  67   3                              }
  68   2                              break;
  69   2      
  70   2                      case 2:  // KEY2 - 温度设定- / 数值减少
  71   2                              if(g_system.system_mode == MODE_SETTING)
  72   2                              {
  73   3                                      if(g_system.menu_index == MENU_TEMP_SET)
  74   3                                      {
  75   4                                              g_system.target_temp -= 1.0;
  76   4                                              if(g_system.target_temp < TEMP_MIN)
  77   4                                                      g_system.target_temp = TEMP_MIN;
  78   4                                              need_update = 1;  // 需要立即更新显示
  79   4                                      }
  80   3                              }
  81   2                              break;
  82   2      
  83   2                      case 3:  // KEY3 - 菜单切换
  84   2                              if(g_system.system_mode == MODE_NORMAL)
  85   2                              {
  86   3                                      g_system.system_mode = MODE_SETTING;
  87   3                                      g_system.menu_index = MENU_TEMP_SET;
  88   3                              }
  89   2                              else
  90   2                              {
  91   3                                      g_system.menu_index++;
  92   3                                      if(g_system.menu_index > MENU_SYSTEM_INFO)
  93   3                                              g_system.menu_index = MENU_TEMP_SET;
  94   3                              }
  95   2                              need_update = 1;  // 菜单切换需要立即更新显示
  96   2                              break;
  97   2      
  98   2                      case 4:  // KEY4 - 确认设置 / 退出菜单
  99   2                              if(g_system.system_mode == MODE_SETTING)
 100   2                              {
 101   3                                      g_system.system_mode = MODE_NORMAL;
 102   3                              }
 103   2                              need_update = 1;  // 模式切换需要立即更新显示
 104   2                              break;
 105   2              }
 106   1      
 107   1              // 如果有按键操作，立即更新显示
 108   1              if(need_update)
 109   1              {
 110   2                      Display_Update();  // 立即刷新显示，解决切换过慢问题
 111   2              }
 112   1      }
 113          
 114          /*-----------------------------------------------------------------------------
 115           * 函数名称: System_Init
 116           * 功能描述: 系统初始化函数
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 09:58:07 PAGE 3   

 117           *----------------------------------------------------------------------------*/
 118          void System_Init(void)
 119          {
 120   1              // 初始化各个模块
 121   1              Motor_Init();      // 电机控制初始化
 122   1              Sensor_Init();     // 传感器初始化
 123   1              DS1302_Init();     // 时钟初始化
 124   1              Alarm_Init();      // 报警初始化
 125   1              Display_Init();    // 显示初始化
 126   1              UART_Init();       // 串口初始化
 127   1      
 128   1              // 初始化时钟（设置合理的初始时间）
 129   1              current_time.year = 24;    // 2024年
 130   1              current_time.month = 1;    // 1月
 131   1              current_time.day = 1;      // 1日
 132   1              current_time.hour = 12;    // 12时
 133   1              current_time.minute = 30;  // 30分
 134   1              current_time.second = 45;  // 45秒
 135   1              DS1302_Set_Time(&current_time);
 136   1      
 137   1              // 延时确保写入完成
 138   1              delay_ms(100);
 139   1      
 140   1              // 发送系统启动信息
 141   1              UART_Send_String("System Started!\r\n");
 142   1      }
 143          
 144          /*定时器0中断初始化函数*/
 145          void Timer0Init(void)           //1毫秒@11.0592MHz
 146          {
 147   1              TMOD &= 0xF0;           //设置定时器模式
 148   1              TMOD |= 0x01;           //设置定时器模式
 149   1              TL0 = 0x66;             //设置定时初值
 150   1              TH0 = 0xFC;             //设置定时初值
 151   1              TF0 = 0;                //清除TF0标志
 152   1              TR0 = 1;                //定时器0开始计时
 153   1              
 154   1              ET0 = 1;
 155   1              EA = 1;
 156   1      }
 157          
 158          
 159          /*-----------------------------------------------------------------------------
 160           * 函数名称: Temperature_Control
 161           * 功能描述: 温度控制算法
 162           *----------------------------------------------------------------------------*/
 163          void Temperature_Control(void)
 164          {
 165   1              float temp_diff;
 166   1              unsigned char motor_dir;
 167   1              unsigned char motor_speed;
 168   1      
 169   1              // 计算温度偏差
 170   1              temp_diff = g_system.current_temp - g_system.target_temp;
 171   1      
 172   1              // 温度控制逻辑
 173   1              if(temp_diff < -TEMP_DEADZONE)  // 当前温度低于设定值
 174   1              {
 175   2                      motor_dir = MOTOR_FORWARD;  // 正转制热
 176   2                      motor_speed = Motor_Calc_Speed(-temp_diff);
 177   2              }
 178   1              else if(temp_diff > TEMP_DEADZONE)  // 当前温度高于设定值
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 09:58:07 PAGE 4   

 179   1              {
 180   2                      motor_dir = MOTOR_REVERSE;  // 反转制冷
 181   2                      motor_speed = Motor_Calc_Speed(temp_diff);
 182   2              }
 183   1              else  // 在死区内
 184   1              {
 185   2                      motor_dir = MOTOR_STOP;     // 停止
 186   2                      motor_speed = 0;
 187   2              }
 188   1      
 189   1              // 控制电机
 190   1              Motor_Control(motor_dir, motor_speed);
 191   1      }
 192          
 193          /*-----------------------------------------------------------------------------
 194           * 函数名称: Timer0Server
 195           * 功能描述: 定时器0中断服务函数 - 多任务调度
 196           *----------------------------------------------------------------------------*/
 197          void Timer0Server() interrupt 1
 198          {
 199   1              // 1ms任务：按键扫描
 200   1              if(++Key_Slow_Down == 10) Key_Slow_Down = 0;
 201   1      
 202   1              // 任务调度计数器
 203   1              task_1ms_counter++;
 204   1              task_10ms_counter++;
 205   1              task_100ms_counter++;
 206   1              task_500ms_counter++;
 207   1              task_1000ms_counter++;
 208   1      }
 209          
 210          
 211          /*-----------------------------------------------------------------------------
 212           * 函数名称: main
 213           * 功能描述: 主函数 - 系统主循环
 214           *----------------------------------------------------------------------------*/
 215          void main()
 216          {
 217   1              // 系统初始化
 218   1              System_Init();
 219   1              Timer0Init();
 220   1      
 221   1              // 主循环
 222   1              while(1)
 223   1              {
 224   2                      // 1ms任务：按键处理
 225   2                      if(task_1ms_counter >= TASK_1MS_PERIOD)
 226   2                      {
 227   3                              task_1ms_counter = 0;
 228   3                              Key_Process();
 229   3                      }
 230   2      
 231   2                      // 10ms任务：ADC数据采集
 232   2                      if(task_10ms_counter >= TASK_10MS_PERIOD)
 233   2                      {
 234   3                              task_10ms_counter = 0;
 235   3                              Sensor_Update_All();      // 更新传感器数据
 236   3                              Motor_Get_Speed_Voltage(); // 读取电机转速电压
 237   3                      }
 238   2      
 239   2                      // 100ms任务：显示更新和温度读取
 240   2                      if(task_100ms_counter >= TASK_100MS_PERIOD)
C51 COMPILER V9.54   MAIN                                                                  07/07/2025 09:58:07 PAGE 5   

 241   2                      {
 242   3                              task_100ms_counter = 0;
 243   3                              g_system.current_temp = rd_temperature();  // 读取DS18B20温度
 244   3                              Display_Update();         // 更新显示
 245   3                      }
 246   2      
 247   2                      // 500ms任务：温度控制、报警处理和时钟读取
 248   2                      if(task_500ms_counter >= TASK_500MS_PERIOD)
 249   2                      {
 250   3                              task_500ms_counter = 0;
 251   3                              DS1302_Get_Time(&current_time);  // 提高时钟读取频率到500ms
 252   3                              Temperature_Control();    // 温度控制算法
 253   3                              Alarm_Process();          // 报警处理
 254   3                      }
 255   2      
 256   2                      // 1000ms任务：串口输出
 257   2                      if(task_1000ms_counter >= TASK_1000MS_PERIOD)
 258   2                      {
 259   3                              task_1000ms_counter = 0;
 260   3                              UART_Send_System_Data();  // 发送系统数据
 261   3                              UART_Send_Alarm_Info();   // 发送报警信息
 262   3                      }
 263   2              }
 264   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    636    ----
   CONSTANT SIZE    =     18    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =     54       7
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
