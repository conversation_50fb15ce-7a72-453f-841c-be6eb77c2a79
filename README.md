# 智能温控保温杯系统

基于STC89C52单片机的智能温控保温杯系统，实现精确温度控制、多传感器监测、渐变式报警和实时数据通信。

## 系统概述

本系统通过直流电机正反转模拟制热制冷过程，利用定时器1生成PWM信号控制电机转速，实现精确的温度控制。系统集成多种传感器进行实时监测，并通过LCD显示和串口通信提供丰富的用户交互功能。

## 最新更新 (V1.1)

### 性能优化
1. **LCD即时响应**：按键操作后屏幕立即刷新，无需等待100ms任务周期
2. **时钟显示优化**：时钟读取频率提升至500ms，显示更加实时
3. **电机PWM控制**：使用定时器1生成真正的PWM信号，电机转速控制更精确

### 功能改进
- 按键响应速度提升10倍（从100ms降至10ms以内）
- 时钟显示更新频率提升1倍（从1000ms降至500ms）
- 电机控制采用硬件PWM，转速控制更稳定

## 核心功能

### 1. 智能温度控制
- **电机模拟温控**：正转制热，反转制冷
- **硬件PWM调速**：使用定时器1生成PWM信号，根据温度偏差动态调整电机转速
- **按键温度设定**：支持20-90℃范围内1℃步进调节，按键响应即时
- **转速电压监测**：实时读取电机转速电压值

### 2. 多传感器检测
- **DS18B20温度传感器**：检测当前环境温度
- **NTC热敏电阻**：监测芯片温度，确保系统安全
- **过流检测**：实时监测电机电流，防止过载
- **电压监测**：检测系统供电和输出电压

### 3. 渐变式报警系统
- **蜂鸣器报警**：频率随温度偏离程度动态变化
- **LED指示灯**：闪烁频率随报警等级调整
- **多级报警**：5级报警等级，越接近限值报警越急促

### 4. 人机交互界面
- **LCD9648显示**：多参数分屏显示，按键操作即时刷新
  - 第1行：当前温度/设定温度
  - 第2行：电机电压和转速
  - 第3行：实时时钟显示（500ms更新）
  - 第4行：NTC温度和电流值
- **按键操作**：4键实现所有设置功能，响应速度<10ms
- **菜单系统**：温度设定、时间设定、系统信息

### 5. 实时数据通信
- **串口输出**：9600波特率
- **数据格式**：`TEMP:25.6,MOTOR:1.2V,NTC:45.2,CURRENT:0.8A,TIME:12:30:45`
- **报警信息**：`ALARM:LEVEL3,DEVIATION:5.2`

## 硬件要求

### 主控芯片
- STC89C52单片机（11.0592MHz晶振）

### 传感器模块
- DS18B20数字温度传感器
- 外部ADC芯片（SPI接口）
- NTC热敏电阻（芯片温度检测）
- 电流检测传感器
- 电机转速电压检测

### 执行器件
- 直流电机（模拟制热制冷）
- 蜂鸣器
- LED指示灯

### 显示与交互
- LCD9648显示屏（96x48像素）
- 4个按键

### 通信与时钟
- DS1302实时时钟芯片
- UART串口通信

## 引脚连接表

| 功能 | 引脚 | 说明 |
|------|------|------|
| 按键1-4 | P1^0-P1^3 | 温度+/-、菜单、确认 |
| DS18B20 | P1^4 | 单总线温度传感器 |
| 蜂鸣器 | P1^5 | 报警输出 |
| 电机控制 | P1^6-P1^7 | 正转/反转控制 |
| LED指示灯 | P0^0-P0^1 | 状态指示 |
| LCD显示 | P0^2-P0^6 | SPI接口 |
| IIC通信 | P2^0-P2^1 | ADC/DAC功能 |
| DS1302时钟 | P2^2-P2^4 | CLK/DAT/RST |
| UART串口 | P3^0-P3^1 | RXD/TXD |
| ADC芯片SPI | P3^2-P3^5 | DO/DI/CS/CLK |

## 操作指南

### 按键功能
- **KEY1**：温度设定增加（+1℃）
- **KEY2**：温度设定减少（-1℃）
- **KEY3**：菜单切换/进入设置模式
- **KEY4**：确认设置/退出菜单

### 菜单操作
1. **正常模式**：显示所有系统参数
2. **设置模式**：按KEY3进入，包含：
   - 温度设定菜单
   - 时间设定菜单
   - 系统信息菜单

### 温度控制逻辑
```
IF (当前温度 < 设定温度 - 1℃)
    电机正转制热，转速 = 偏差 × 50
ELSE IF (当前温度 > 设定温度 + 1℃)
    电机反转制冷，转速 = 偏差 × 50
ELSE
    电机停止
```

## 技术参数

### 温度控制
- **设定范围**：20-90℃
- **控制精度**：±1℃
- **死区范围**：±1℃
- **响应时间**：<5秒

### 传感器精度
- **DS18B20**：±0.5℃
- **NTC温度**：±1℃
- **电流检测**：±0.01A
- **电压检测**：±0.1V

### 报警系统
- **报警等级**：0-5级
- **蜂鸣器频率**：1000-4000Hz
- **LED闪烁周期**：100-500ms

### 通信参数
- **串口波特率**：9600bps
- **数据位**：8位
- **停止位**：1位
- **校验位**：无

## 串口数据格式

### 系统数据输出（每秒一次）
```
TEMP:25.6,MOTOR:1.2V,NTC:45.2,CURRENT:0.8A,TIME:12:30:45,TARGET:25.0,MOTOR_STATE:1,MOTOR_SPEED:128
```

### 报警信息输出（有报警时）
```
ALARM:LEVEL3,DEVIATION:5.2
```

## 故障排除

### 常见问题
1. **温度读取异常**
   - 检查DS18B20连接
   - 确认P1^4引脚无冲突

2. **电机不转**
   - 检查P1^6、P1^7引脚输出
   - 确认定时器1 PWM信号正常
   - 检查电机供电和连接

3. **显示异常**
   - 检查LCD连接线
   - 确认SPI通信正常

4. **串口无输出**
   - 检查波特率设置
   - 确认P3^0、P3^1连接

### 调试建议
- 使用串口监控系统状态
- 观察LED指示灯判断报警等级
- 通过LCD菜单查看系统信息

## 开发环境

- **编译器**：Keil C51
- **仿真器**：STC-ISP
- **调试工具**：串口调试助手

## 版本信息

- **版本**：V1.1
- **日期**：2025年1月
- **作者**：智能温控系统开发团队

### 版本更新记录
- **V1.1 (2025-01-07)**
  - 修复LCD切换过慢问题，实现按键即时响应
  - 优化时钟显示频率，从1000ms提升至500ms
  - 重构电机控制，使用定时器1硬件PWM替代DAC模拟PWM
  - 提升系统整体响应速度和控制精度

- **V1.0 (2025-01-01)**
  - 初始版本发布
  - 实现基本温控功能
  - 集成多传感器监测
  - 完成LCD显示和串口通信

## 注意事项

1. **安全使用**
   - 确保电源电压在规定范围内
   - 注意电机功率不要超载
   - 定期检查NTC温度防止过热

2. **维护保养**
   - 定期清洁传感器
   - 检查连接线路
   - 更新时钟电池

3. **扩展功能**
   - 支持添加更多传感器
   - 可扩展网络通信功能
   - 支持数据记录和分析
