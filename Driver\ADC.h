#ifndef __ADC_H__
#define __ADC_H__

#include "Config.h"

/*=============================================================================
 * ADC芯片SPI接口驱动头文件
 * 功能：外部ADC芯片通信，多通道模拟量采集
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * ADC通道定义
 *----------------------------------------------------------------------------*/
#define ADC_CH_MOTOR_VOLTAGE    0     // 通道0：电机转速电压检测
#define ADC_CH_NTC_TEMP         1     // 通道1：NTC芯片温度检测  
#define ADC_CH_CURRENT_DETECT   2     // 通道2：过流检测
#define ADC_CH_RESERVED         3     // 通道3：预留

/*-----------------------------------------------------------------------------
 * 函数声明
 *----------------------------------------------------------------------------*/
void ADC_Init(void);                           // ADC初始化
unsigned int ADC_Read_Channel(unsigned char ch); // 读取指定通道ADC值
float ADC_Get_Motor_Voltage(void);             // 获取电机转速电压
float ADC_Get_NTC_Temperature(void);           // 获取NTC芯片温度
float ADC_Get_Current_Detect(void);            // 获取过流检测值

#endif // __ADC_H__
