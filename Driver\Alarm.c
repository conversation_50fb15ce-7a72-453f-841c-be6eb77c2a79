#include "Alarm.h"

/*=============================================================================
 * 渐变报警模块实现
 * 功能：蜂鸣器频率控制、LED闪烁控制、渐变式报警
 *============================================================================*/

// 静态变量
static unsigned int buzzer_counter = 0;  // 蜂鸣器计数器
static unsigned int led_counter = 0;     // LED计数器
static unsigned char buzzer_state = 0;   // 蜂鸣器状态
static unsigned char led_state = 0;      // LED状态

/*-----------------------------------------------------------------------------
 * 函数名称: Alarm_Init
 * 功能描述: 报警模块初始化
 *----------------------------------------------------------------------------*/
void Alarm_Init(void)
{
    BUZZER = 0;  // 蜂鸣器初始化为关闭
    LED1 = 0;    // LED1初始化为熄灭
    LED2 = 0;    // LED2初始化为熄灭

    buzzer_counter = 0;
    led_counter = 0;
    buzzer_state = 0;
    led_state = 0;
}

/*-----------------------------------------------------------------------------
 * 函数名称: Buzzer_Control
 * 功能描述: 蜂鸣器频率控制
 * 输入参数: enable - 使能标志, freq - 频率(Hz)
 *----------------------------------------------------------------------------*/
void Buzzer_Control(unsigned char enable, unsigned int freq)
{
    if(enable && freq > 0)
    {
        // 计算蜂鸣器切换周期（基于1ms中断）
        unsigned int period = 1000 / (freq * 2);  // 半周期
        
        buzzer_counter++;
        if(buzzer_counter >= period)
        {
            buzzer_counter = 0;
            buzzer_state = !buzzer_state;
            BUZZER = buzzer_state;
        }
    }
    else
    {
        BUZZER = 0;
        buzzer_state = 0;
        buzzer_counter = 0;
    }
}

/*-----------------------------------------------------------------------------
 * 函数名称: LED_Control
 * 功能描述: LED闪烁控制
 * 输入参数: led_num - LED编号(1或2), state - 状态(0关闭,1常亮,2闪烁)
 *----------------------------------------------------------------------------*/
void LED_Control(unsigned char led_num, unsigned char state)
{
    switch(state)
    {
        case 0:  // 关闭
            if(led_num == 1) LED1 = 0;
            else if(led_num == 2) LED2 = 0;
            break;
            
        case 1:  // 常亮
            if(led_num == 1) LED1 = 1;
            else if(led_num == 2) LED2 = 1;
            break;
            
        case 2:  // 闪烁（在Update_Alarm_Output中处理）
            break;
    }
}

/*-----------------------------------------------------------------------------
 * 函数名称: Calc_Alarm_Level
 * 功能描述: 根据温度偏差计算报警等级
 * 输入参数: deviation - 温度偏差绝对值(℃)
 * 返回值: 报警等级(0-5)
 *----------------------------------------------------------------------------*/
unsigned char Calc_Alarm_Level(float deviation)
{
    unsigned char level;

    // 根据偏差程度计算报警等级（调整阈值使报警更敏感）
    if(deviation < 1.0)
        level = 0;  // 正常
    else if(deviation < 3.0)
        level = 1;  // 轻微偏差
    else if(deviation < 5.0)
        level = 2;  // 中等偏差
    else if(deviation < 8.0)
        level = 3;  // 较大偏差
    else if(deviation < 12.0)
        level = 4;  // 严重偏差
    else
        level = 5;  // 极严重偏差

    return level;
}

/*-----------------------------------------------------------------------------
 * 函数名称: Alarm_Process
 * 功能描述: 报警处理主函数
 *----------------------------------------------------------------------------*/
void Alarm_Process(void)
{
    float temp_deviation;
    unsigned char alarm_level;
    unsigned int buzzer_freq;
    
    // 计算温度偏差
    temp_deviation = g_system.current_temp - g_system.target_temp;
    if(temp_deviation < 0) temp_deviation = -temp_deviation;  // 取绝对值
    
    // 计算报警等级
    alarm_level = Calc_Alarm_Level(temp_deviation);
    g_system.alarm_level = alarm_level;
    
    // 根据报警等级设置蜂鸣器频率
    if(alarm_level == 0)
    {
        buzzer_freq = 0;  // 无报警
    }
    else
    {
        // 频率随报警等级显著增加，使变化更明显
        // 等级1: 1500Hz, 等级2: 2000Hz, 等级3: 2500Hz, 等级4: 3000Hz, 等级5: 3500Hz
        buzzer_freq = BUZZER_BASE_FREQ + (alarm_level * 500);
    }
    
    // 控制蜂鸣器
    Buzzer_Control((alarm_level > 0), buzzer_freq);
    
    // 更新报警输出
    Update_Alarm_Output();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Update_Alarm_Output
 * 功能描述: 更新报警输出（LED闪烁等）
 *----------------------------------------------------------------------------*/
void Update_Alarm_Output(void)
{
    unsigned int led_period;

    // 根据报警等级设置LED闪烁周期
    if(g_system.alarm_level == 0)
    {
        LED1 = 0;  // 正常时LED熄灭
        LED2 = 0;
        led_counter = 0;
        led_state = 0;
    }
    else
    {
        // LED闪烁周期随报警等级显著减少，使频率变化肉眼可见
        // 等级1: 500ms, 等级2: 250ms, 等级3: 167ms, 等级4: 125ms, 等级5: 100ms
        led_period = LED_BASE_PERIOD / g_system.alarm_level;
        if(led_period < 100) led_period = 100;  // 最快100ms

        led_counter++;
        if(led_counter >= led_period)
        {
            led_counter = 0;
            led_state = !led_state;

            // 根据报警等级控制不同LED
            if(g_system.alarm_level <= 2)
            {
                LED1 = led_state;  // 轻微报警用LED1
                LED2 = 0;
            }
            else if(g_system.alarm_level <= 4)
            {
                LED1 = led_state;  // 中等报警LED1快闪
                LED2 = !led_state; // LED2反相闪烁
            }
            else
            {
                LED1 = led_state;  // 严重报警两个LED同步快闪
                LED2 = led_state;
            }
        }
    }
}
