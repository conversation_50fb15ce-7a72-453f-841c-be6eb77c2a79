C51 COMPILER V9.54   KEY                                                                   07/07/2025 00:36:34 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE KEY
OBJECT MODULE PLACED IN .\Objects\Key.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\Key.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTEXTE
                    -ND PRINT(.\Listings\Key.lst) OBJECT(.\Objects\Key.obj)

line level    source

   1          #include "Key.h"
   2          #include "Config.h"  // 引用统一配置文件
   3          
   4          unsigned char Key_Read()  // 读取按键状态，返回按键编号
   5          {
   6   1              unsigned char temp = 0;
   7   1      
   8   1              if(KEY1 == 0) temp = 1;  // 温度设定+
   9   1              if(KEY2 == 0) temp = 2;  // 温度设定-
  10   1              if(KEY3 == 0) temp = 3;  // 菜单切换
  11   1              if(KEY4 == 0) temp = 4;  // 确认设置
  12   1      
  13   1              return temp;
  14   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =     22    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
