C51 COMPILER V9.54   DS1302                                                                07/07/2025 00:36:35 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE DS1302
OBJECT MODULE PLACED IN .\Objects\DS1302.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\DS1302.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTE
                    -XTEND PRINT(.\Listings\DS1302.lst) OBJECT(.\Objects\DS1302.obj)

line level    source

   1          #include "DS1302.h"
   2          #include "Delay.h"
   3          
   4          /*=============================================================================
   5           * DS1302时钟模块实现
   6           * 功能：实时时钟读取、设置、时间显示
   7           *============================================================================*/
   8          
   9          /*-----------------------------------------------------------------------------
  10           * 函数名称: DS1302_Init
  11           * 功能描述: DS1302时钟芯片初始化
  12           *----------------------------------------------------------------------------*/
  13          void DS1302_Init(void)
  14          {
  15   1          DS1302_RST = 0;  // 复位引脚初始化
  16   1          DS1302_CLK = 0;  // 时钟引脚初始化
  17   1          DS1302_DAT = 0;  // 数据引脚初始化
  18   1      
  19   1          // 关闭写保护
  20   1          DS1302_Write_Byte(DS1302_CONTROL, 0x00);
  21   1      
  22   1          // 启动时钟（清除秒寄存器的CH位）
  23   1          DS1302_Write_Byte(DS1302_SECOND, 0x00);
  24   1      }
  25          
  26          /*-----------------------------------------------------------------------------
  27           * 函数名称: DS1302_Write_Byte
  28           * 功能描述: 向DS1302写入一个字节
  29           * 输入参数: addr - 寄存器地址, dat - 写入数据
  30           *----------------------------------------------------------------------------*/
  31          void DS1302_Write_Byte(unsigned char addr, unsigned char dat)
  32          {
  33   1          unsigned char i;
  34   1          
  35   1          DS1302_RST = 1;  // 启动传输
  36   1          delay_10us(1);
  37   1          
  38   1          // 发送地址
  39   1          for(i = 0; i < 8; i++)
  40   1          {
  41   2              DS1302_CLK = 0;
  42   2              DS1302_DAT = (addr >> i) & 0x01;
  43   2              delay_10us(1);
  44   2              DS1302_CLK = 1;
  45   2              delay_10us(1);
  46   2          }
  47   1          
  48   1          // 发送数据
  49   1          for(i = 0; i < 8; i++)
  50   1          {
  51   2              DS1302_CLK = 0;
  52   2              DS1302_DAT = (dat >> i) & 0x01;
  53   2              delay_10us(1);
  54   2              DS1302_CLK = 1;
C51 COMPILER V9.54   DS1302                                                                07/07/2025 00:36:35 PAGE 2   

  55   2              delay_10us(1);
  56   2          }
  57   1          
  58   1          DS1302_RST = 0;  // 结束传输
  59   1          delay_10us(1);
  60   1      }
  61          
  62          /*-----------------------------------------------------------------------------
  63           * 函数名称: DS1302_Read_Byte
  64           * 功能描述: 从DS1302读取一个字节
  65           * 输入参数: addr - 寄存器地址
  66           * 返回值: 读取的数据
  67           *----------------------------------------------------------------------------*/
  68          unsigned char DS1302_Read_Byte(unsigned char addr)
  69          {
  70   1          unsigned char i, dat = 0;
  71   1          
  72   1          DS1302_RST = 1;  // 启动传输
  73   1          delay_10us(1);
  74   1          
  75   1          // 发送地址（读命令）
  76   1          addr |= 0x01;  // 设置读标志
  77   1          for(i = 0; i < 8; i++)
  78   1          {
  79   2              DS1302_CLK = 0;
  80   2              DS1302_DAT = (addr >> i) & 0x01;
  81   2              delay_10us(1);
  82   2              DS1302_CLK = 1;
  83   2              delay_10us(1);
  84   2          }
  85   1          
  86   1          // 读取数据
  87   1          for(i = 0; i < 8; i++)
  88   1          {
  89   2              DS1302_CLK = 0;
  90   2              delay_10us(1);
  91   2              DS1302_CLK = 1;
  92   2              if(DS1302_DAT) dat |= (0x01 << i);
  93   2              delay_10us(1);
  94   2          }
  95   1          
  96   1          DS1302_RST = 0;  // 结束传输
  97   1          delay_10us(1);
  98   1          
  99   1          return dat;
 100   1      }
 101          
 102          /*-----------------------------------------------------------------------------
 103           * 函数名称: DS1302_Set_Time
 104           * 功能描述: 设置DS1302时间
 105           * 输入参数: time - 时间结构体指针
 106           *----------------------------------------------------------------------------*/
 107          void DS1302_Set_Time(Time_t *time)
 108          {
 109   1          DS1302_Write_Byte(DS1302_SECOND, Decimal_To_BCD(time->second));
 110   1          DS1302_Write_Byte(DS1302_MINUTE, Decimal_To_BCD(time->minute));
 111   1          DS1302_Write_Byte(DS1302_HOUR,   Decimal_To_BCD(time->hour));
 112   1          DS1302_Write_Byte(DS1302_DATE,   Decimal_To_BCD(time->day));
 113   1          DS1302_Write_Byte(DS1302_MONTH,  Decimal_To_BCD(time->month));
 114   1          DS1302_Write_Byte(DS1302_YEAR,   Decimal_To_BCD(time->year));
 115   1      }
 116          
C51 COMPILER V9.54   DS1302                                                                07/07/2025 00:36:35 PAGE 3   

 117          /*-----------------------------------------------------------------------------
 118           * 函数名称: DS1302_Get_Time
 119           * 功能描述: 读取DS1302时间
 120           * 输入参数: time - 时间结构体指针
 121           *----------------------------------------------------------------------------*/
 122          void DS1302_Get_Time(Time_t *time)
 123          {
 124   1          unsigned char raw_sec, raw_min, raw_hour;
 125   1      
 126   1          // 读取原始数据
 127   1          raw_sec = DS1302_Read_Byte(DS1302_SECOND);
 128   1          raw_min = DS1302_Read_Byte(DS1302_MINUTE);
 129   1          raw_hour = DS1302_Read_Byte(DS1302_HOUR);
 130   1      
 131   1          // 检查数据有效性，如果无效则使用默认值
 132   1          if((raw_sec & 0x7F) > 0x59 || (raw_min & 0x7F) > 0x59 || (raw_hour & 0x3F) > 0x23)
 133   1          {
 134   2              // 数据无效，使用默认时间
 135   2              time->second = 0;
 136   2              time->minute = 0;
 137   2              time->hour = 12;
 138   2              time->day = 1;
 139   2              time->month = 1;
 140   2              time->year = 24;
 141   2          }
 142   1          else
 143   1          {
 144   2              // 数据有效，进行BCD转换
 145   2              time->second = BCD_To_Decimal(raw_sec & 0x7F);
 146   2              time->minute = BCD_To_Decimal(raw_min & 0x7F);
 147   2              time->hour   = BCD_To_Decimal(raw_hour & 0x3F);
 148   2              time->day    = BCD_To_Decimal(DS1302_Read_Byte(DS1302_DATE) & 0x3F);
 149   2              time->month  = BCD_To_Decimal(DS1302_Read_Byte(DS1302_MONTH) & 0x1F);
 150   2              time->year   = BCD_To_Decimal(DS1302_Read_Byte(DS1302_YEAR));
 151   2          }
 152   1      
 153   1          // 更新全局时间变量
 154   1          g_system.year = time->year;
 155   1          g_system.month = time->month;
 156   1          g_system.day = time->day;
 157   1          g_system.hour = time->hour;
 158   1          g_system.minute = time->minute;
 159   1          g_system.second = time->second;
 160   1      }
 161          
 162          /*-----------------------------------------------------------------------------
 163           * 函数名称: BCD_To_Decimal
 164           * 功能描述: BCD码转十进制
 165           *----------------------------------------------------------------------------*/
 166          unsigned char BCD_To_Decimal(unsigned char bcd)
 167          {
 168   1          return ((bcd >> 4) * 10 + (bcd & 0x0F));
 169   1      }
 170          
 171          /*-----------------------------------------------------------------------------
 172           * 函数名称: Decimal_To_BCD
 173           * 功能描述: 十进制转BCD码
 174           *----------------------------------------------------------------------------*/
 175          unsigned char Decimal_To_BCD(unsigned char dec)
 176          {
 177   1          return ((dec / 10) << 4) + (dec % 10);
 178   1      }
C51 COMPILER V9.54   DS1302                                                                07/07/2025 00:36:35 PAGE 4   



MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    682    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----      15
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
