#include "ADC.h"
#include "Delay.h"

/*=============================================================================
 * ADC芯片SPI接口驱动实现
 * 功能：外部ADC芯片通信，多通道模拟量采集
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数名称: ADC_Init
 * 功能描述: ADC芯片初始化
 *----------------------------------------------------------------------------*/
void ADC_Init(void)
{
    ADC_CS = 1;   // 片选信号初始化为高电平
    ADC_CLK = 0;  // 时钟信号初始化为低电平
    ADC_DI = 0;   // 数据输入初始化为低电平
}

/*-----------------------------------------------------------------------------
 * 函数名称: ADC_SPI_Write_Byte
 * 功能描述: SPI写入一个字节
 * 输入参数: dat - 要写入的数据
 *----------------------------------------------------------------------------*/
void ADC_SPI_Write_Byte(unsigned char dat)
{
    unsigned char i;
    
    for(i = 0; i < 8; i++)
    {
        ADC_CLK = 0;
        if(dat & 0x80)
            ADC_DI = 1;
        else
            ADC_DI = 0;
        delay_10us(1);
        ADC_CLK = 1;
        delay_10us(1);
        dat <<= 1;
    }
    ADC_CLK = 0;
}

/*-----------------------------------------------------------------------------
 * 函数名称: ADC_SPI_Read_Byte
 * 功能描述: SPI读取一个字节
 * 返回值: 读取的数据
 *----------------------------------------------------------------------------*/
unsigned char ADC_SPI_Read_Byte(void)
{
    unsigned char i, dat = 0;
    
    for(i = 0; i < 8; i++)
    {
        ADC_CLK = 0;
        delay_10us(1);
        ADC_CLK = 1;
        dat <<= 1;
        if(ADC_DO)
            dat |= 0x01;
        delay_10us(1);
    }
    ADC_CLK = 0;
    return dat;
}

/*-----------------------------------------------------------------------------
 * 函数名称: ADC_Read_Channel
 * 功能描述: 读取指定通道的ADC值
 * 输入参数: ch - 通道号(0-3)
 * 返回值: 12位ADC值(0-4095)
 *----------------------------------------------------------------------------*/
unsigned int ADC_Read_Channel(unsigned char ch)
{
    unsigned char cmd;
    unsigned char high_byte, low_byte;
    unsigned int adc_value;
    
    // 构造命令字节：启动位+单端模式+通道选择
    cmd = 0x80 | ((ch & 0x03) << 4);
    
    // 开始SPI通信
    ADC_CS = 0;
    delay_10us(1);
    
    // 发送命令
    ADC_SPI_Write_Byte(cmd);
    
    // 读取高字节
    high_byte = ADC_SPI_Read_Byte();
    
    // 读取低字节
    low_byte = ADC_SPI_Read_Byte();
    
    // 结束通信
    ADC_CS = 1;
    
    // 组合12位ADC值
    adc_value = ((unsigned int)(high_byte & 0x0F) << 8) | low_byte;
    
    return adc_value;
}

/*-----------------------------------------------------------------------------
 * 函数名称: ADC_Get_Motor_Voltage
 * 功能描述: 获取电机转速电压
 * 返回值: 电机转速电压(V)
 *----------------------------------------------------------------------------*/
float ADC_Get_Motor_Voltage(void)
{
    unsigned int adc_value;
    float voltage;
    
    adc_value = ADC_Read_Channel(ADC_CH_MOTOR_VOLTAGE);
    
    // 转换为电压值：12位ADC，参考电压5V，考虑分压比3:1
    voltage = (float)adc_value * 5.0 / 4096.0 * 3.0;
    
    // 更新全局变量
    g_system.motor_voltage = voltage;
    
    return voltage;
}

/*-----------------------------------------------------------------------------
 * 函数名称: ADC_Get_NTC_Temperature
 * 功能描述: 获取NTC芯片温度
 * 返回值: 芯片温度(℃)
 *----------------------------------------------------------------------------*/
float ADC_Get_NTC_Temperature(void)
{
    unsigned int adc_value;
    float voltage, temperature;
    
    adc_value = ADC_Read_Channel(ADC_CH_NTC_TEMP);
    
    // 转换为电压值
    voltage = (float)adc_value * 5.0 / 4096.0;
    
    // NTC温度转换（简化线性转换，实际应用需要更精确的算法）
    temperature = (voltage - 1.25) * 40 + 25;  // 假设1.25V对应25℃
    
    // 温度范围限制
    if(temperature < NTC_TEMP_MIN) temperature = NTC_TEMP_MIN;
    if(temperature > NTC_TEMP_MAX) temperature = NTC_TEMP_MAX;
    
    // 更新全局变量
    g_system.chip_temp = temperature;
    
    return temperature;
}

/*-----------------------------------------------------------------------------
 * 函数名称: ADC_Get_Current_Detect
 * 功能描述: 获取过流检测值
 * 返回值: 电流检测值(A)
 *----------------------------------------------------------------------------*/
float ADC_Get_Current_Detect(void)
{
    unsigned int adc_value;
    float voltage, current;
    
    adc_value = ADC_Read_Channel(ADC_CH_CURRENT_DETECT);
    
    // 转换为电压值
    voltage = (float)adc_value * 5.0 / 4096.0;
    
    // 电流转换（假设电流传感器输出2.5V对应0A，每A对应0.4V）
    current = (voltage - 2.5) / 0.4;
    
    // 电流范围限制
    if(current < 0) current = 0;
    if(current > CURRENT_MAX) current = CURRENT_MAX;
    
    // 更新全局变量
    g_system.current_detect = current;
    
    return current;
}
