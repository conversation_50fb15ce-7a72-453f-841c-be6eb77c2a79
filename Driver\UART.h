#ifndef __UART_H__
#define __UART_H__

#include "Config.h"

/*=============================================================================
 * 串口通信模块头文件
 * 功能：串口初始化、数据发送、系统状态输出
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数声明
 *----------------------------------------------------------------------------*/
void UART_Init(void);                        // 串口初始化
void UART_Send_Byte(unsigned char dat);      // 发送单字节
void UART_Send_String(unsigned char *str);   // 发送字符串
void UART_Send_System_Data(void);            // 发送系统数据
void UART_Send_Alarm_Info(void);             // 发送报警信息
void UART_Send_Float(float value, unsigned char precision);  // 发送浮点数

#endif // __UART_H__
