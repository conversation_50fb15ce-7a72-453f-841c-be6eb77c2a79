C51 COMPILER V9.54   ADC                                                                   07/07/2025 00:36:36 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE ADC
OBJECT MODULE PLACED IN .\Objects\ADC.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\ADC.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTEXTE
                    -ND PRINT(.\Listings\ADC.lst) OBJECT(.\Objects\ADC.obj)

line level    source

   1          #include "ADC.h"
   2          #include "Delay.h"
   3          
   4          /*=============================================================================
   5           * ADC芯片SPI接口驱动实现
   6           * 功能：外部ADC芯片通信，多通道模拟量采集
   7           *============================================================================*/
   8          
   9          /*-----------------------------------------------------------------------------
  10           * 函数名称: ADC_Init
  11           * 功能描述: ADC芯片初始化
  12           *----------------------------------------------------------------------------*/
  13          void ADC_Init(void)
  14          {
  15   1          ADC_CS = 1;   // 片选信号初始化为高电平
  16   1          ADC_CLK = 0;  // 时钟信号初始化为低电平
  17   1          ADC_DI = 0;   // 数据输入初始化为低电平
  18   1      }
  19          
  20          /*-----------------------------------------------------------------------------
  21           * 函数名称: ADC_SPI_Write_Byte
  22           * 功能描述: SPI写入一个字节
  23           * 输入参数: dat - 要写入的数据
  24           *----------------------------------------------------------------------------*/
  25          void ADC_SPI_Write_Byte(unsigned char dat)
  26          {
  27   1          unsigned char i;
  28   1          
  29   1          for(i = 0; i < 8; i++)
  30   1          {
  31   2              ADC_CLK = 0;
  32   2              if(dat & 0x80)
  33   2                  ADC_DI = 1;
  34   2              else
  35   2                  ADC_DI = 0;
  36   2              delay_10us(1);
  37   2              ADC_CLK = 1;
  38   2              delay_10us(1);
  39   2              dat <<= 1;
  40   2          }
  41   1          ADC_CLK = 0;
  42   1      }
  43          
  44          /*-----------------------------------------------------------------------------
  45           * 函数名称: ADC_SPI_Read_Byte
  46           * 功能描述: SPI读取一个字节
  47           * 返回值: 读取的数据
  48           *----------------------------------------------------------------------------*/
  49          unsigned char ADC_SPI_Read_Byte(void)
  50          {
  51   1          unsigned char i, dat = 0;
  52   1          
  53   1          for(i = 0; i < 8; i++)
  54   1          {
C51 COMPILER V9.54   ADC                                                                   07/07/2025 00:36:36 PAGE 2   

  55   2              ADC_CLK = 0;
  56   2              delay_10us(1);
  57   2              ADC_CLK = 1;
  58   2              dat <<= 1;
  59   2              if(ADC_DO)
  60   2                  dat |= 0x01;
  61   2              delay_10us(1);
  62   2          }
  63   1          ADC_CLK = 0;
  64   1          return dat;
  65   1      }
  66          
  67          /*-----------------------------------------------------------------------------
  68           * 函数名称: ADC_Read_Channel
  69           * 功能描述: 读取指定通道的ADC值
  70           * 输入参数: ch - 通道号(0-3)
  71           * 返回值: 12位ADC值(0-4095)
  72           *----------------------------------------------------------------------------*/
  73          unsigned int ADC_Read_Channel(unsigned char ch)
  74          {
  75   1          unsigned char cmd;
  76   1          unsigned char high_byte, low_byte;
  77   1          unsigned int adc_value;
  78   1          
  79   1          // 构造命令字节：启动位+单端模式+通道选择
  80   1          cmd = 0x80 | ((ch & 0x03) << 4);
  81   1          
  82   1          // 开始SPI通信
  83   1          ADC_CS = 0;
  84   1          delay_10us(1);
  85   1          
  86   1          // 发送命令
  87   1          ADC_SPI_Write_Byte(cmd);
  88   1          
  89   1          // 读取高字节
  90   1          high_byte = ADC_SPI_Read_Byte();
  91   1          
  92   1          // 读取低字节
  93   1          low_byte = ADC_SPI_Read_Byte();
  94   1          
  95   1          // 结束通信
  96   1          ADC_CS = 1;
  97   1          
  98   1          // 组合12位ADC值
  99   1          adc_value = ((unsigned int)(high_byte & 0x0F) << 8) | low_byte;
 100   1          
 101   1          return adc_value;
 102   1      }
 103          
 104          /*-----------------------------------------------------------------------------
 105           * 函数名称: ADC_Get_Motor_Voltage
 106           * 功能描述: 获取电机转速电压
 107           * 返回值: 电机转速电压(V)
 108           *----------------------------------------------------------------------------*/
 109          float ADC_Get_Motor_Voltage(void)
 110          {
 111   1          unsigned int adc_value;
 112   1          float voltage;
 113   1          
 114   1          adc_value = ADC_Read_Channel(ADC_CH_MOTOR_VOLTAGE);
 115   1          
 116   1          // 转换为电压值：12位ADC，参考电压5V，考虑分压比3:1
C51 COMPILER V9.54   ADC                                                                   07/07/2025 00:36:36 PAGE 3   

 117   1          voltage = (float)adc_value * 5.0 / 4096.0 * 3.0;
 118   1          
 119   1          // 更新全局变量
 120   1          g_system.motor_voltage = voltage;
 121   1          
 122   1          return voltage;
 123   1      }
 124          
 125          /*-----------------------------------------------------------------------------
 126           * 函数名称: ADC_Get_NTC_Temperature
 127           * 功能描述: 获取NTC芯片温度
 128           * 返回值: 芯片温度(℃)
 129           *----------------------------------------------------------------------------*/
 130          float ADC_Get_NTC_Temperature(void)
 131          {
 132   1          unsigned int adc_value;
 133   1          float voltage, temperature;
 134   1          
 135   1          adc_value = ADC_Read_Channel(ADC_CH_NTC_TEMP);
 136   1          
 137   1          // 转换为电压值
 138   1          voltage = (float)adc_value * 5.0 / 4096.0;
 139   1          
 140   1          // NTC温度转换（简化线性转换，实际应用需要更精确的算法）
 141   1          temperature = (voltage - 1.25) * 40 + 25;  // 假设1.25V对应25℃
 142   1          
 143   1          // 温度范围限制
 144   1          if(temperature < NTC_TEMP_MIN) temperature = NTC_TEMP_MIN;
 145   1          if(temperature > NTC_TEMP_MAX) temperature = NTC_TEMP_MAX;
 146   1          
 147   1          // 更新全局变量
 148   1          g_system.chip_temp = temperature;
 149   1          
 150   1          return temperature;
 151   1      }
 152          
 153          /*-----------------------------------------------------------------------------
 154           * 函数名称: ADC_Get_Current_Detect
 155           * 功能描述: 获取过流检测值
 156           * 返回值: 电流检测值(A)
 157           *----------------------------------------------------------------------------*/
 158          float ADC_Get_Current_Detect(void)
 159          {
 160   1          unsigned int adc_value;
 161   1          float voltage, current;
 162   1          
 163   1          adc_value = ADC_Read_Channel(ADC_CH_CURRENT_DETECT);
 164   1          
 165   1          // 转换为电压值
 166   1          voltage = (float)adc_value * 5.0 / 4096.0;
 167   1          
 168   1          // 电流转换（假设电流传感器输出2.5V对应0A，每A对应0.4V）
 169   1          current = (voltage - 2.5) / 0.4;
 170   1          
 171   1          // 电流范围限制
 172   1          if(current < 0) current = 0;
 173   1          if(current > CURRENT_MAX) current = CURRENT_MAX;
 174   1          
 175   1          // 更新全局变量
 176   1          g_system.current_detect = current;
 177   1          
 178   1          return current;
C51 COMPILER V9.54   ADC                                                                   07/07/2025 00:36:36 PAGE 4   

 179   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    533    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----      26
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
