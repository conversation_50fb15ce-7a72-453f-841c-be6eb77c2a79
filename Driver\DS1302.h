#ifndef __DS1302_H__
#define __DS1302_H__

#include "Config.h"

/*=============================================================================
 * DS1302时钟模块头文件
 * 功能：实时时钟读取、设置、时间显示
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * DS1302寄存器地址定义
 *----------------------------------------------------------------------------*/
#define DS1302_SECOND       0x80  // 秒寄存器
#define DS1302_MINUTE       0x82  // 分寄存器
#define DS1302_HOUR         0x84  // 时寄存器
#define DS1302_DATE         0x86  // 日寄存器
#define DS1302_MONTH        0x88  // 月寄存器
#define DS1302_DAY          0x8A  // 星期寄存器
#define DS1302_YEAR         0x8C  // 年寄存器
#define DS1302_CONTROL      0x8E  // 控制寄存器

/*-----------------------------------------------------------------------------
 * 时间结构体定义
 *----------------------------------------------------------------------------*/
typedef struct {
    unsigned char year;    // 年(0-99)
    unsigned char month;   // 月(1-12)
    unsigned char day;     // 日(1-31)
    unsigned char hour;    // 时(0-23)
    unsigned char minute;  // 分(0-59)
    unsigned char second;  // 秒(0-59)
} Time_t;

/*-----------------------------------------------------------------------------
 * 函数声明
 *----------------------------------------------------------------------------*/
void DS1302_Init(void);                    // DS1302初始化
void DS1302_Write_Byte(unsigned char addr, unsigned char dat);  // 写字节
unsigned char DS1302_Read_Byte(unsigned char addr);             // 读字节
void DS1302_Set_Time(Time_t *time);        // 设置时间
void DS1302_Get_Time(Time_t *time);        // 读取时间
unsigned char BCD_To_Decimal(unsigned char bcd);  // BCD转十进制
unsigned char Decimal_To_BCD(unsigned char dec);  // 十进制转BCD

#endif // __DS1302_H__
