C51 COMPILER V9.54   ALARM                                                                 07/07/2025 00:36:35 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE ALARM
OBJECT MODULE PLACED IN .\Objects\Alarm.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\Alarm.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTEX
                    -TEND PRINT(.\Listings\Alarm.lst) OBJECT(.\Objects\Alarm.obj)

line level    source

   1          #include "Alarm.h"
   2          
   3          /*=============================================================================
   4           * 渐变报警模块实现
   5           * 功能：蜂鸣器频率控制、LED闪烁控制、渐变式报警
   6           *============================================================================*/
   7          
   8          // 静态变量
   9          static unsigned int buzzer_counter = 0;  // 蜂鸣器计数器
  10          static unsigned int led_counter = 0;     // LED计数器
  11          static unsigned char buzzer_state = 0;   // 蜂鸣器状态
  12          static unsigned char led_state = 0;      // LED状态
  13          
  14          /*-----------------------------------------------------------------------------
  15           * 函数名称: Alarm_Init
  16           * 功能描述: 报警模块初始化
  17           *----------------------------------------------------------------------------*/
  18          void Alarm_Init(void)
  19          {
  20   1          BUZZER = 0;  // 蜂鸣器初始化为关闭
  21   1          LED1 = 0;    // LED1初始化为熄灭
  22   1          LED2 = 0;    // LED2初始化为熄灭
  23   1      
  24   1          buzzer_counter = 0;
  25   1          led_counter = 0;
  26   1          buzzer_state = 0;
  27   1          led_state = 0;
  28   1      }
  29          
  30          /*-----------------------------------------------------------------------------
  31           * 函数名称: Buzzer_Control
  32           * 功能描述: 蜂鸣器频率控制
  33           * 输入参数: enable - 使能标志, freq - 频率(Hz)
  34           *----------------------------------------------------------------------------*/
  35          void Buzzer_Control(unsigned char enable, unsigned int freq)
  36          {
  37   1          if(enable && freq > 0)
  38   1          {
  39   2              // 计算蜂鸣器切换周期（基于1ms中断）
  40   2              unsigned int period = 1000 / (freq * 2);  // 半周期
  41   2              
  42   2              buzzer_counter++;
  43   2              if(buzzer_counter >= period)
  44   2              {
  45   3                  buzzer_counter = 0;
  46   3                  buzzer_state = !buzzer_state;
  47   3                  BUZZER = buzzer_state;
  48   3              }
  49   2          }
  50   1          else
  51   1          {
  52   2              BUZZER = 0;
  53   2              buzzer_state = 0;
  54   2              buzzer_counter = 0;
C51 COMPILER V9.54   ALARM                                                                 07/07/2025 00:36:35 PAGE 2   

  55   2          }
  56   1      }
  57          
  58          /*-----------------------------------------------------------------------------
  59           * 函数名称: LED_Control
  60           * 功能描述: LED闪烁控制
  61           * 输入参数: led_num - LED编号(1或2), state - 状态(0关闭,1常亮,2闪烁)
  62           *----------------------------------------------------------------------------*/
  63          void LED_Control(unsigned char led_num, unsigned char state)
  64          {
  65   1          switch(state)
  66   1          {
  67   2              case 0:  // 关闭
  68   2                  if(led_num == 1) LED1 = 0;
  69   2                  else if(led_num == 2) LED2 = 0;
  70   2                  break;
  71   2                  
  72   2              case 1:  // 常亮
  73   2                  if(led_num == 1) LED1 = 1;
  74   2                  else if(led_num == 2) LED2 = 1;
  75   2                  break;
  76   2                  
  77   2              case 2:  // 闪烁（在Update_Alarm_Output中处理）
  78   2                  break;
  79   2          }
  80   1      }
  81          
  82          /*-----------------------------------------------------------------------------
  83           * 函数名称: Calc_Alarm_Level
  84           * 功能描述: 根据温度偏差计算报警等级
  85           * 输入参数: deviation - 温度偏差绝对值(℃)
  86           * 返回值: 报警等级(0-5)
  87           *----------------------------------------------------------------------------*/
  88          unsigned char Calc_Alarm_Level(float deviation)
  89          {
  90   1          unsigned char level;
  91   1      
  92   1          // 根据偏差程度计算报警等级（调整阈值使报警更敏感）
  93   1          if(deviation < 1.0)
  94   1              level = 0;  // 正常
  95   1          else if(deviation < 3.0)
  96   1              level = 1;  // 轻微偏差
  97   1          else if(deviation < 5.0)
  98   1              level = 2;  // 中等偏差
  99   1          else if(deviation < 8.0)
 100   1              level = 3;  // 较大偏差
 101   1          else if(deviation < 12.0)
 102   1              level = 4;  // 严重偏差
 103   1          else
 104   1              level = 5;  // 极严重偏差
 105   1      
 106   1          return level;
 107   1      }
 108          
 109          /*-----------------------------------------------------------------------------
 110           * 函数名称: Alarm_Process
 111           * 功能描述: 报警处理主函数
 112           *----------------------------------------------------------------------------*/
 113          void Alarm_Process(void)
 114          {
 115   1          float temp_deviation;
 116   1          unsigned char alarm_level;
C51 COMPILER V9.54   ALARM                                                                 07/07/2025 00:36:35 PAGE 3   

 117   1          unsigned int buzzer_freq;
 118   1          
 119   1          // 计算温度偏差
 120   1          temp_deviation = g_system.current_temp - g_system.target_temp;
 121   1          if(temp_deviation < 0) temp_deviation = -temp_deviation;  // 取绝对值
 122   1          
 123   1          // 计算报警等级
 124   1          alarm_level = Calc_Alarm_Level(temp_deviation);
 125   1          g_system.alarm_level = alarm_level;
 126   1          
 127   1          // 根据报警等级设置蜂鸣器频率
 128   1          if(alarm_level == 0)
 129   1          {
 130   2              buzzer_freq = 0;  // 无报警
 131   2          }
 132   1          else
 133   1          {
 134   2              // 频率随报警等级显著增加，使变化更明显
 135   2              // 等级1: 1500Hz, 等级2: 2000Hz, 等级3: 2500Hz, 等级4: 3000Hz, 等级5: 3500Hz
 136   2              buzzer_freq = BUZZER_BASE_FREQ + (alarm_level * 500);
 137   2          }
 138   1          
 139   1          // 控制蜂鸣器
 140   1          Buzzer_Control((alarm_level > 0), buzzer_freq);
 141   1          
 142   1          // 更新报警输出
 143   1          Update_Alarm_Output();
 144   1      }
 145          
 146          /*-----------------------------------------------------------------------------
 147           * 函数名称: Update_Alarm_Output
 148           * 功能描述: 更新报警输出（LED闪烁等）
 149           *----------------------------------------------------------------------------*/
 150          void Update_Alarm_Output(void)
 151          {
 152   1          unsigned int led_period;
 153   1      
 154   1          // 根据报警等级设置LED闪烁周期
 155   1          if(g_system.alarm_level == 0)
 156   1          {
 157   2              LED1 = 0;  // 正常时LED熄灭
 158   2              LED2 = 0;
 159   2              led_counter = 0;
 160   2              led_state = 0;
 161   2          }
 162   1          else
 163   1          {
 164   2              // LED闪烁周期随报警等级显著减少，使频率变化肉眼可见
 165   2              // 等级1: 500ms, 等级2: 250ms, 等级3: 167ms, 等级4: 125ms, 等级5: 100ms
 166   2              led_period = LED_BASE_PERIOD / g_system.alarm_level;
 167   2              if(led_period < 100) led_period = 100;  // 最快100ms
 168   2      
 169   2              led_counter++;
 170   2              if(led_counter >= led_period)
 171   2              {
 172   3                  led_counter = 0;
 173   3                  led_state = !led_state;
 174   3      
 175   3                  // 根据报警等级控制不同LED
 176   3                  if(g_system.alarm_level <= 2)
 177   3                  {
 178   4                      LED1 = led_state;  // 轻微报警用LED1
C51 COMPILER V9.54   ALARM                                                                 07/07/2025 00:36:35 PAGE 4   

 179   4                      LED2 = 0;
 180   4                  }
 181   3                  else if(g_system.alarm_level <= 4)
 182   3                  {
 183   4                      LED1 = led_state;  // 中等报警LED1快闪
 184   4                      LED2 = !led_state; // LED2反相闪烁
 185   4                  }
 186   3                  else
 187   3                  {
 188   4                      LED1 = led_state;  // 严重报警两个LED同步快闪
 189   4                      LED2 = led_state;
 190   4                  }
 191   3              }
 192   2          }
 193   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    513    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =      6      13
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
