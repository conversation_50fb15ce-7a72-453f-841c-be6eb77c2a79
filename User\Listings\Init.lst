C51 COMPILER V9.54   INIT                                                                  07/06/2025 13:17:27 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE INIT
OBJECT MODULE PLACED IN .\Objects\Init.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\Init.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTEXT
                    -END PRINT(.\Listings\Init.lst) OBJECT(.\Objects\Init.obj)

line level    source

   1          #include <STC15F2K60S2.H>
   2          #include "Init.h"
   3          
   4          
   5          void System_Init()
   6          {
   7   1              P0 = 0xFF;//熄灭LED
   8   1              P2 = P2 & 0x1F | 0x80;//LED通道对应Y4C使能
   9   1              P2 = P2 & 0x1F;//关闭通道
  10   1              
  11   1              P0 = 0x00;//关闭外设
  12   1              P2 = P2 & 0x1F | 0xA0;//Y5C使能
  13   1              P2 = P2 & 0x1F;//关闭通道
  14   1              
  15   1              //先赋值P0，然后使寄存器使能，读取到P0的值，再关闭通道
  16   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =     29    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
