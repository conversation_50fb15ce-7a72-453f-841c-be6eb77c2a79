#include "DS1302.h"
#include "Delay.h"

/*=============================================================================
 * DS1302时钟模块实现
 * 功能：实时时钟读取、设置、时间显示
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数名称: DS1302_Init
 * 功能描述: DS1302时钟芯片初始化
 *----------------------------------------------------------------------------*/
void DS1302_Init(void)
{
    DS1302_RST = 0;  // 复位引脚初始化
    DS1302_CLK = 0;  // 时钟引脚初始化
    DS1302_DAT = 0;  // 数据引脚初始化

    // 关闭写保护
    DS1302_Write_Byte(DS1302_CONTROL, 0x00);

    // 启动时钟（清除秒寄存器的CH位）
    DS1302_Write_Byte(DS1302_SECOND, 0x00);
}

/*-----------------------------------------------------------------------------
 * 函数名称: DS1302_Write_Byte
 * 功能描述: 向DS1302写入一个字节
 * 输入参数: addr - 寄存器地址, dat - 写入数据
 *----------------------------------------------------------------------------*/
void DS1302_Write_Byte(unsigned char addr, unsigned char dat)
{
    unsigned char i;
    
    DS1302_RST = 1;  // 启动传输
    delay_10us(1);
    
    // 发送地址
    for(i = 0; i < 8; i++)
    {
        DS1302_CLK = 0;
        DS1302_DAT = (addr >> i) & 0x01;
        delay_10us(1);
        DS1302_CLK = 1;
        delay_10us(1);
    }
    
    // 发送数据
    for(i = 0; i < 8; i++)
    {
        DS1302_CLK = 0;
        DS1302_DAT = (dat >> i) & 0x01;
        delay_10us(1);
        DS1302_CLK = 1;
        delay_10us(1);
    }
    
    DS1302_RST = 0;  // 结束传输
    delay_10us(1);
}

/*-----------------------------------------------------------------------------
 * 函数名称: DS1302_Read_Byte
 * 功能描述: 从DS1302读取一个字节
 * 输入参数: addr - 寄存器地址
 * 返回值: 读取的数据
 *----------------------------------------------------------------------------*/
unsigned char DS1302_Read_Byte(unsigned char addr)
{
    unsigned char i, dat = 0;
    
    DS1302_RST = 1;  // 启动传输
    delay_10us(1);
    
    // 发送地址（读命令）
    addr |= 0x01;  // 设置读标志
    for(i = 0; i < 8; i++)
    {
        DS1302_CLK = 0;
        DS1302_DAT = (addr >> i) & 0x01;
        delay_10us(1);
        DS1302_CLK = 1;
        delay_10us(1);
    }
    
    // 读取数据
    for(i = 0; i < 8; i++)
    {
        DS1302_CLK = 0;
        delay_10us(1);
        DS1302_CLK = 1;
        if(DS1302_DAT) dat |= (0x01 << i);
        delay_10us(1);
    }
    
    DS1302_RST = 0;  // 结束传输
    delay_10us(1);
    
    return dat;
}

/*-----------------------------------------------------------------------------
 * 函数名称: DS1302_Set_Time
 * 功能描述: 设置DS1302时间
 * 输入参数: time - 时间结构体指针
 *----------------------------------------------------------------------------*/
void DS1302_Set_Time(Time_t *time)
{
    DS1302_Write_Byte(DS1302_SECOND, Decimal_To_BCD(time->second));
    DS1302_Write_Byte(DS1302_MINUTE, Decimal_To_BCD(time->minute));
    DS1302_Write_Byte(DS1302_HOUR,   Decimal_To_BCD(time->hour));
    DS1302_Write_Byte(DS1302_DATE,   Decimal_To_BCD(time->day));
    DS1302_Write_Byte(DS1302_MONTH,  Decimal_To_BCD(time->month));
    DS1302_Write_Byte(DS1302_YEAR,   Decimal_To_BCD(time->year));
}

/*-----------------------------------------------------------------------------
 * 函数名称: DS1302_Get_Time
 * 功能描述: 读取DS1302时间
 * 输入参数: time - 时间结构体指针
 *----------------------------------------------------------------------------*/
void DS1302_Get_Time(Time_t *time)
{
    unsigned char raw_sec, raw_min, raw_hour;

    // 读取原始数据
    raw_sec = DS1302_Read_Byte(DS1302_SECOND);
    raw_min = DS1302_Read_Byte(DS1302_MINUTE);
    raw_hour = DS1302_Read_Byte(DS1302_HOUR);

    // 检查数据有效性，如果无效则使用默认值
    if((raw_sec & 0x7F) > 0x59 || (raw_min & 0x7F) > 0x59 || (raw_hour & 0x3F) > 0x23)
    {
        // 数据无效，使用默认时间
        time->second = 0;
        time->minute = 0;
        time->hour = 12;
        time->day = 1;
        time->month = 1;
        time->year = 24;
    }
    else
    {
        // 数据有效，进行BCD转换
        time->second = BCD_To_Decimal(raw_sec & 0x7F);
        time->minute = BCD_To_Decimal(raw_min & 0x7F);
        time->hour   = BCD_To_Decimal(raw_hour & 0x3F);
        time->day    = BCD_To_Decimal(DS1302_Read_Byte(DS1302_DATE) & 0x3F);
        time->month  = BCD_To_Decimal(DS1302_Read_Byte(DS1302_MONTH) & 0x1F);
        time->year   = BCD_To_Decimal(DS1302_Read_Byte(DS1302_YEAR));
    }

    // 更新全局时间变量
    g_system.year = time->year;
    g_system.month = time->month;
    g_system.day = time->day;
    g_system.hour = time->hour;
    g_system.minute = time->minute;
    g_system.second = time->second;
}

/*-----------------------------------------------------------------------------
 * 函数名称: BCD_To_Decimal
 * 功能描述: BCD码转十进制
 *----------------------------------------------------------------------------*/
unsigned char BCD_To_Decimal(unsigned char bcd)
{
    return ((bcd >> 4) * 10 + (bcd & 0x0F));
}

/*-----------------------------------------------------------------------------
 * 函数名称: Decimal_To_BCD
 * 功能描述: 十进制转BCD码
 *----------------------------------------------------------------------------*/
unsigned char Decimal_To_BCD(unsigned char dec)
{
    return ((dec / 10) << 4) + (dec % 10);
}
