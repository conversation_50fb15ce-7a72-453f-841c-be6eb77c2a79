#ifndef __MOTOR_H__
#define __MOTOR_H__

#include "Config.h"

/*=============================================================================
 * 电机控制模块头文件
 * 功能：电机正反转控制、PWM调速、转速电压检测
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数声明
 *----------------------------------------------------------------------------*/
void Motor_Init(void);                           // 电机初始化
void Timer1_PWM_Init(void);                      // 定时器1 PWM初始化
void Motor_Control(unsigned char dir, unsigned char speed);  // 电机控制
void Motor_Stop(void);                           // 电机停止
float Motor_Get_Speed_Voltage(void);             // 获取电机转速电压
unsigned char Motor_Calc_Speed(float temp_diff); // 根据温差计算转速

#endif // __MOTOR_H__
