#ifndef __SENSOR_H__
#define __SENSOR_H__

#include "Config.h"

/*=============================================================================
 * 传感器检测模块头文件
 * 功能：NTC芯片温度检测、过流检测、多通道ADC采集
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数声明
 *----------------------------------------------------------------------------*/
void Sensor_Init(void);              // 传感器初始化
float Get_NTC_Temperature(void);     // 获取NTC芯片温度
float Get_Current_Detect(void);      // 获取过流检测值
void Sensor_Update_All(void);        // 更新所有传感器数据

#endif // __SENSOR_H__
