#ifndef __DISPLAY_H__
#define __DISPLAY_H__

#include "Config.h"
#include "LCD9648.h"

/*=============================================================================
 * 显示功能扩展模块头文件
 * 功能：LCD多参数显示、菜单界面、数据格式化
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数声明
 *----------------------------------------------------------------------------*/
void Display_Init(void);                     // 显示初始化
void Display_Main_Screen(void);              // 主界面显示
void Display_Menu(void);                     // 菜单界面显示
void Display_Update(void);                   // 显示更新
void Display_Float(unsigned char x, unsigned char y, float value, unsigned char precision);  // 显示浮点数
void Display_Time(unsigned char x, unsigned char y);  // 显示时间

#endif // __DISPLAY_H__
