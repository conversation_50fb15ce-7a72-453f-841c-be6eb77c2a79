C51 COMPILER V9.54   MOTOR                                                                 07/07/2025 09:58:07 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE MOTOR
OBJECT MODULE PLACED IN .\Objects\Motor.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\Motor.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTEX
                    -TEND PRINT(.\Listings\Motor.lst) OBJECT(.\Objects\Motor.obj)

line level    source

   1          #include "Motor.h"
   2          #include "iic.h"
   3          #include "ADC.h"
   4          
   5          /*=============================================================================
   6           * 电机控制模块实现
   7           * 功能：电机正反转控制、PWM调速、转速电压检测
   8           *============================================================================*/
   9          
  10          // PWM相关变量
  11          static unsigned char pwm_duty = 0;        // PWM占空比(0-255)
  12          static unsigned char pwm_counter = 0;     // PWM计数器
  13          static unsigned char motor_direction = 0; // 电机方向
  14          
  15          /*-----------------------------------------------------------------------------
  16           * 函数名称: Motor_Init
  17           * 功能描述: 电机控制引脚初始化和定时器1 PWM初始化
  18           *----------------------------------------------------------------------------*/
  19          void Motor_Init(void)
  20          {
  21   1          MOTOR_FWD = 0;  // 正转引脚初始化为低电平
  22   1          MOTOR_REV = 0;  // 反转引脚初始化为低电平
  23   1      
  24   1          // 初始化PWM变量
  25   1          pwm_duty = 0;
  26   1          pwm_counter = 0;
  27   1          motor_direction = MOTOR_STOP;
  28   1      
  29   1          // 初始化定时器1用于PWM生成
  30   1          Timer1_PWM_Init();
  31   1      }
  32          
  33          /*-----------------------------------------------------------------------------
  34           * 函数名称: Timer1_PWM_Init
  35           * 功能描述: 定时器1 PWM初始化
  36           *----------------------------------------------------------------------------*/
  37          void Timer1_PWM_Init(void)
  38          {
  39   1          // 定时器1工作模式设置
  40   1          TMOD &= 0x0F;  // 清除定时器1模式位
  41   1          TMOD |= 0x10;  // 定时器1工作在模式1（16位定时器）
  42   1      
  43   1          // 设置定时器1初值（产生约1KHz PWM频率）
  44   1          TH1 = 0xFC;    // 定时器1高8位初值
  45   1          TL1 = 0x66;    // 定时器1低8位初值
  46   1      
  47   1          // 启动定时器1
  48   1          TR1 = 1;       // 启动定时器1
  49   1          ET1 = 1;       // 使能定时器1中断
  50   1      }
  51          
  52          /*-----------------------------------------------------------------------------
  53           * 函数名称: Motor_Control
  54           * 功能描述: 电机方向和转速控制（使用PWM）
C51 COMPILER V9.54   MOTOR                                                                 07/07/2025 09:58:07 PAGE 2   

  55           * 输入参数: dir - 电机方向(0停止,1正转,2反转), speed - 转速(0-255)
  56           *----------------------------------------------------------------------------*/
  57          void Motor_Control(unsigned char dir, unsigned char speed)
  58          {
  59   1          // 设置PWM占空比
  60   1          pwm_duty = speed;
  61   1          motor_direction = dir;
  62   1      
  63   1          // 根据方向和速度控制电机
  64   1          if(speed == 0 || dir == MOTOR_STOP)
  65   1          {
  66   2              // 停止电机
  67   2              MOTOR_FWD = 0;
  68   2              MOTOR_REV = 0;
  69   2              pwm_duty = 0;
  70   2          }
  71   1      
  72   1          // 更新全局状态
  73   1          g_system.motor_state = dir;
  74   1          g_system.motor_speed = speed;
  75   1      
  76   1          // 同时输出DAC信号用于转速电压检测
  77   1          Da_Write(speed);  // 保留DAC输出用于电压检测
  78   1      }
  79          
  80          /*-----------------------------------------------------------------------------
  81           * 函数名称: Motor_Stop
  82           * 功能描述: 停止电机运行
  83           *----------------------------------------------------------------------------*/
  84          void Motor_Stop(void)
  85          {
  86   1          Motor_Control(MOTOR_STOP, 0);
  87   1      }
  88          
  89          /*-----------------------------------------------------------------------------
  90           * 函数名称: Motor_Get_Speed_Voltage
  91           * 功能描述: 读取电机转速电压值
  92           * 返回值: 电机转速电压(V)
  93           *----------------------------------------------------------------------------*/
  94          float Motor_Get_Speed_Voltage(void)
  95          {
  96   1          // 直接调用ADC驱动函数
  97   1          return ADC_Get_Motor_Voltage();
  98   1      }
  99          
 100          /*-----------------------------------------------------------------------------
 101           * 函数名称: Timer1_PWM_ISR
 102           * 功能描述: 定时器1中断服务函数 - 生成PWM信号
 103           *----------------------------------------------------------------------------*/
 104          void Timer1_PWM_ISR() interrupt 3
 105          {
 106   1          // 重新装载定时器1初值
 107   1          TH1 = 0xFC;
 108   1          TL1 = 0x66;
 109   1      
 110   1          // PWM计数器递增
 111   1          pwm_counter++;
 112   1      
 113   1          // 根据PWM占空比和方向控制电机引脚
 114   1          if(motor_direction != MOTOR_STOP && pwm_duty > 0)
 115   1          {
 116   2              if(pwm_counter < pwm_duty)  // PWM高电平期间
C51 COMPILER V9.54   MOTOR                                                                 07/07/2025 09:58:07 PAGE 3   

 117   2              {
 118   3                  switch(motor_direction)
 119   3                  {
 120   4                      case MOTOR_FORWARD:  // 正转
 121   4                          MOTOR_FWD = 1;
 122   4                          MOTOR_REV = 0;
 123   4                          break;
 124   4                      case MOTOR_REVERSE:  // 反转
 125   4                          MOTOR_FWD = 0;
 126   4                          MOTOR_REV = 1;
 127   4                          break;
 128   4                  }
 129   3              }
 130   2              else  // PWM低电平期间
 131   2              {
 132   3                  MOTOR_FWD = 0;
 133   3                  MOTOR_REV = 0;
 134   3              }
 135   2          }
 136   1          else  // 停止状态
 137   1          {
 138   2              MOTOR_FWD = 0;
 139   2              MOTOR_REV = 0;
 140   2          }
 141   1      }
 142          
 143          /*-----------------------------------------------------------------------------
 144           * 函数名称: Motor_Calc_Speed
 145           * 功能描述: 根据温度偏差计算电机转速
 146           * 输入参数: temp_diff - 温度偏差(℃)
 147           * 返回值: 电机转速(0-255)
 148           *----------------------------------------------------------------------------*/
 149          unsigned char Motor_Calc_Speed(float temp_diff)
 150          {
 151   1          unsigned char speed;
 152   1          float abs_diff;
 153   1      
 154   1          // 计算温度偏差绝对值
 155   1          abs_diff = (temp_diff > 0) ? temp_diff : -temp_diff;
 156   1      
 157   1          // 温度偏差转换为转速（偏差越大转速越快）
 158   1          speed = (unsigned char)(abs_diff * 50);  // 转速系数可调整
 159   1      
 160   1          // 限制转速范围
 161   1          if(speed > MOTOR_SPEED_MAX) speed = MOTOR_SPEED_MAX;
 162   1          if(speed < MOTOR_SPEED_MIN) speed = MOTOR_SPEED_MIN;
 163   1      
 164   1          return speed;
 165   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =    223    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =      3       8
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
