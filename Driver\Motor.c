#include "Motor.h"
#include "iic.h"
#include "ADC.h"

/*=============================================================================
 * 电机控制模块实现
 * 功能：电机正反转控制、PWM调速、转速电压检测
 *============================================================================*/

// PWM相关变量
static unsigned char pwm_duty = 0;        // PWM占空比(0-255)
static unsigned char pwm_counter = 0;     // PWM计数器
static unsigned char motor_direction = 0; // 电机方向

/*-----------------------------------------------------------------------------
 * 函数名称: Motor_Init
 * 功能描述: 电机控制引脚初始化和定时器1 PWM初始化
 *----------------------------------------------------------------------------*/
void Motor_Init(void)
{
    MOTOR_FWD = 0;  // 正转引脚初始化为低电平
    MOTOR_REV = 0;  // 反转引脚初始化为低电平

    // 初始化PWM变量
    pwm_duty = 0;
    pwm_counter = 0;
    motor_direction = MOTOR_STOP;

    // 初始化定时器1用于PWM生成
    Timer1_PWM_Init();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Timer1_PWM_Init
 * 功能描述: 定时器1 PWM初始化
 *----------------------------------------------------------------------------*/
void Timer1_PWM_Init(void)
{
    // 定时器1工作模式设置
    TMOD &= 0x0F;  // 清除定时器1模式位
    TMOD |= 0x10;  // 定时器1工作在模式1（16位定时器）

    // 设置定时器1初值（产生约1KHz PWM频率）
    TH1 = 0xFC;    // 定时器1高8位初值
    TL1 = 0x66;    // 定时器1低8位初值

    // 启动定时器1
    TR1 = 1;       // 启动定时器1
    ET1 = 1;       // 使能定时器1中断
}

/*-----------------------------------------------------------------------------
 * 函数名称: Motor_Control
 * 功能描述: 电机方向和转速控制（使用PWM）
 * 输入参数: dir - 电机方向(0停止,1正转,2反转), speed - 转速(0-255)
 *----------------------------------------------------------------------------*/
void Motor_Control(unsigned char dir, unsigned char speed)
{
    // 设置PWM占空比
    pwm_duty = speed;
    motor_direction = dir;

    // 根据方向和速度控制电机
    if(speed == 0 || dir == MOTOR_STOP)
    {
        // 停止电机
        MOTOR_FWD = 0;
        MOTOR_REV = 0;
        pwm_duty = 0;
    }

    // 更新全局状态
    g_system.motor_state = dir;
    g_system.motor_speed = speed;

    // 同时输出DAC信号用于转速电压检测
    Da_Write(speed);  // 保留DAC输出用于电压检测
}

/*-----------------------------------------------------------------------------
 * 函数名称: Motor_Stop
 * 功能描述: 停止电机运行
 *----------------------------------------------------------------------------*/
void Motor_Stop(void)
{
    Motor_Control(MOTOR_STOP, 0);
}

/*-----------------------------------------------------------------------------
 * 函数名称: Motor_Get_Speed_Voltage
 * 功能描述: 读取电机转速电压值
 * 返回值: 电机转速电压(V)
 *----------------------------------------------------------------------------*/
float Motor_Get_Speed_Voltage(void)
{
    // 直接调用ADC驱动函数
    return ADC_Get_Motor_Voltage();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Timer1_PWM_ISR
 * 功能描述: 定时器1中断服务函数 - 生成PWM信号
 *----------------------------------------------------------------------------*/
void Timer1_PWM_ISR() interrupt 3
{
    // 重新装载定时器1初值
    TH1 = 0xFC;
    TL1 = 0x66;

    // PWM计数器递增
    pwm_counter++;

    // 根据PWM占空比和方向控制电机引脚
    if(motor_direction != MOTOR_STOP && pwm_duty > 0)
    {
        if(pwm_counter < pwm_duty)  // PWM高电平期间
        {
            switch(motor_direction)
            {
                case MOTOR_FORWARD:  // 正转
                    MOTOR_FWD = 1;
                    MOTOR_REV = 0;
                    break;
                case MOTOR_REVERSE:  // 反转
                    MOTOR_FWD = 0;
                    MOTOR_REV = 1;
                    break;
            }
        }
        else  // PWM低电平期间
        {
            MOTOR_FWD = 0;
            MOTOR_REV = 0;
        }
    }
    else  // 停止状态
    {
        MOTOR_FWD = 0;
        MOTOR_REV = 0;
    }
}

/*-----------------------------------------------------------------------------
 * 函数名称: Motor_Calc_Speed
 * 功能描述: 根据温度偏差计算电机转速
 * 输入参数: temp_diff - 温度偏差(℃)
 * 返回值: 电机转速(0-255)
 *----------------------------------------------------------------------------*/
unsigned char Motor_Calc_Speed(float temp_diff)
{
    unsigned char speed;
    float abs_diff;

    // 计算温度偏差绝对值
    abs_diff = (temp_diff > 0) ? temp_diff : -temp_diff;

    // 温度偏差转换为转速（偏差越大转速越快）
    speed = (unsigned char)(abs_diff * 50);  // 转速系数可调整

    // 限制转速范围
    if(speed > MOTOR_SPEED_MAX) speed = MOTOR_SPEED_MAX;
    if(speed < MOTOR_SPEED_MIN) speed = MOTOR_SPEED_MIN;

    return speed;
}
