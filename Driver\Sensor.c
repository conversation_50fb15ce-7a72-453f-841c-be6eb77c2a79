#include "Sensor.h"
#include "ADC.h"

/*=============================================================================
 * 传感器检测模块实现
 * 功能：NTC芯片温度检测、过流检测、多通道ADC采集
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数名称: Sensor_Init
 * 功能描述: 传感器模块初始化
 *----------------------------------------------------------------------------*/
void Sensor_Init(void)
{
    // 初始化外部ADC芯片
    ADC_Init();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Get_NTC_Temperature
 * 功能描述: 读取NTC热敏电阻芯片温度
 * 返回值: 芯片温度(℃)
 *----------------------------------------------------------------------------*/
float Get_NTC_Temperature(void)
{
    // 直接调用ADC驱动函数
    return ADC_Get_NTC_Temperature();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Get_Current_Detect
 * 功能描述: 读取过流检测值
 * 返回值: 电流检测值(A)
 *----------------------------------------------------------------------------*/
float Get_Current_Detect(void)
{
    // 直接调用ADC驱动函数
    return ADC_Get_Current_Detect();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Sensor_Update_All
 * 功能描述: 更新所有传感器数据
 *----------------------------------------------------------------------------*/
void Sensor_Update_All(void)
{
    // 更新NTC芯片温度
    Get_NTC_Temperature();

    // 更新过流检测值
    Get_Current_Detect();

    // 更新电机转速电压
    ADC_Get_Motor_Voltage();
}
