C51 COMPILER V9.54   DISPLAY                                                               07/07/2025 00:36:35 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE DISPLAY
OBJECT MODULE PLACED IN .\Objects\Display.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\Display.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECT
                    -EXTEND PRINT(.\Listings\Display.lst) OBJECT(.\Objects\Display.obj)

line level    source

   1          #include "Display.h"
   2          
   3          /*=============================================================================
   4           * 显示功能扩展模块实现
   5           * 功能：LCD多参数显示、菜单界面、数据格式化
   6           *============================================================================*/
   7          
   8          /*-----------------------------------------------------------------------------
   9           * 函数名称: Display_Init
  10           * 功能描述: 显示模块初始化
  11           *----------------------------------------------------------------------------*/
  12          void Display_Init(void)
  13          {
  14   1          lcd9648_init();   // 初始化LCD
  15   1          lcd9648_clear();  // 清屏
  16   1      }
  17          
  18          /*-----------------------------------------------------------------------------
  19           * 函数名称: Display_Float
  20           * 功能描述: 在指定位置显示浮点数
  21           * 输入参数: x,y - 坐标, value - 数值, precision - 小数位数
  22           *----------------------------------------------------------------------------*/
  23          void Display_Float(unsigned char x, unsigned char y, float value, unsigned char precision)
  24          {
  25   1          unsigned int integer_part;
  26   1          unsigned int decimal_part;
  27   1          unsigned char pos = x;
  28   1      
  29   1          // 处理负数
  30   1          if(value < 0)
  31   1          {
  32   2              lcd_show_char(pos, y, '-', 12, 0);
  33   2              pos += 6;
  34   2              value = -value;
  35   2          }
  36   1      
  37   1          // 限制数值范围，避免溢出
  38   1          if(value > 999.9) value = 999.9;
  39   1      
  40   1          // 整数部分
  41   1          integer_part = (unsigned int)value;
  42   1      
  43   1          // 显示整数部分（最多3位）
  44   1          if(integer_part >= 100)
  45   1          {
  46   2              lcd_show_num(pos, y, integer_part, 3, 12, 0);
  47   2              pos += 18;
  48   2          }
  49   1          else if(integer_part >= 10)
  50   1          {
  51   2              lcd_show_num(pos, y, integer_part, 2, 12, 0);
  52   2              pos += 12;
  53   2          }
  54   1          else
C51 COMPILER V9.54   DISPLAY                                                               07/07/2025 00:36:35 PAGE 2   

  55   1          {
  56   2              lcd_show_num(pos, y, integer_part, 1, 12, 0);
  57   2              pos += 6;
  58   2          }
  59   1      
  60   1          // 小数点和小数部分
  61   1          if(precision > 0 && pos < (LCD_WIDTH - 12))
  62   1          {
  63   2              lcd_show_char(pos, y, '.', 12, 0);
  64   2              pos += 6;
  65   2      
  66   2              // 计算小数部分（只显示1位小数）
  67   2              decimal_part = (unsigned int)((value - integer_part) * 10);
  68   2              if(decimal_part > 9) decimal_part = 9;
  69   2      
  70   2              lcd_show_char(pos, y, '0' + decimal_part, 12, 0);
  71   2          }
  72   1      }
  73          
  74          /*-----------------------------------------------------------------------------
  75           * 函数名称: Display_Time
  76           * 功能描述: 在指定位置显示时间
  77           * 输入参数: x,y - 坐标
  78           *----------------------------------------------------------------------------*/
  79          void Display_Time(unsigned char x, unsigned char y)
  80          {
  81   1          unsigned char pos = x;
  82   1          
  83   1          // 显示时:分:秒
  84   1          if(g_system.hour < 10)
  85   1          {
  86   2              lcd_show_char(pos, y, '0', 12, 0);
  87   2              pos += 6;
  88   2          }
  89   1          lcd_show_num(pos, y, g_system.hour, (g_system.hour >= 10) ? 2 : 1, 12, 0);
  90   1          pos += (g_system.hour >= 10) ? 12 : 6;
  91   1          
  92   1          lcd_show_char(pos, y, ':', 12, 0);
  93   1          pos += 6;
  94   1          
  95   1          if(g_system.minute < 10)
  96   1          {
  97   2              lcd_show_char(pos, y, '0', 12, 0);
  98   2              pos += 6;
  99   2          }
 100   1          lcd_show_num(pos, y, g_system.minute, (g_system.minute >= 10) ? 2 : 1, 12, 0);
 101   1          pos += (g_system.minute >= 10) ? 12 : 6;
 102   1          
 103   1          lcd_show_char(pos, y, ':', 12, 0);
 104   1          pos += 6;
 105   1          
 106   1          if(g_system.second < 10)
 107   1          {
 108   2              lcd_show_char(pos, y, '0', 12, 0);
 109   2              pos += 6;
 110   2          }
 111   1          lcd_show_num(pos, y, g_system.second, (g_system.second >= 10) ? 2 : 1, 12, 0);
 112   1      }
 113          
 114          /*-----------------------------------------------------------------------------
 115           * 函数名称: Display_Main_Screen
 116           * 功能描述: 主界面显示
C51 COMPILER V9.54   DISPLAY                                                               07/07/2025 00:36:35 PAGE 3   

 117           *----------------------------------------------------------------------------*/
 118          void Display_Main_Screen(void)
 119          {
 120   1          // 清除显示缓存，避免残留字符
 121   1          lcd9648_clear();
 122   1          
 123   1          // 第1行：温度信息
 124   1          lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "T:");
 125   1          Display_Float(12, 0, g_system.current_temp, 1);
 126   1          lcd_show_string(42, 0, LCD_WIDTH, 12, 12, "/");
 127   1          Display_Float(48, 0, g_system.target_temp, 1);
 128   1          lcd_show_string(78, 0, LCD_WIDTH, 12, 12, "C");
 129   1          
 130   1          // 第2行：电机状态
 131   1          lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "M:");
 132   1          Display_Float(12, 12, g_system.motor_voltage, 1);
 133   1          lcd_show_string(36, 12, LCD_WIDTH, 12, 12, "V S:");
 134   1          lcd_show_num(54, 12, g_system.motor_speed, 3, 12, 0);
 135   1          
 136   1          // 第3行：时钟显示
 137   1          lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "Time:");
 138   1          Display_Time(30, 24);
 139   1          
 140   1          // 第4行：系统状态（使用修正后的Display_Float）
 141   1          lcd_show_string(0, 36, LCD_WIDTH, 12, 12, "NTC:");
 142   1          Display_Float(24, 36, g_system.chip_temp, 1);
 143   1          lcd_show_string(42, 36, LCD_WIDTH, 12, 12, " I:");
 144   1          Display_Float(54, 36, g_system.current_detect, 1);
 145   1          lcd_show_string(78, 36, LCD_WIDTH, 12, 12, "A");
 146   1          
 147   1          // 刷新显示
 148   1          lcd_reflash_gram();
 149   1      }
 150          
 151          /*-----------------------------------------------------------------------------
 152           * 函数名称: Display_Menu
 153           * 功能描述: 菜单界面显示
 154           *----------------------------------------------------------------------------*/
 155          void Display_Menu(void)
 156          {
 157   1          lcd9648_clear();
 158   1          
 159   1          switch(g_system.menu_index)
 160   1          {
 161   2              case MENU_TEMP_SET:
 162   2                  lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "Set Temperature");
 163   2                  lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "Target:");
 164   2                  Display_Float(42, 12, g_system.target_temp, 1);
 165   2                  lcd_show_string(72, 12, LCD_WIDTH, 12, 12, "C");
 166   2                  lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "K1:+ K2:- K4:OK");
 167   2                  break;
 168   2                  
 169   2              case MENU_TIME_SET:
 170   2                  lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "Set Time");
 171   2                  lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "Time:");
 172   2                  Display_Time(30, 12);
 173   2                  lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "K1:+ K2:- K4:OK");
 174   2                  break;
 175   2                  
 176   2              case MENU_SYSTEM_INFO:
 177   2                  lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "System Info");
 178   2                  lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "Alarm Lv:");
C51 COMPILER V9.54   DISPLAY                                                               07/07/2025 00:36:35 PAGE 4   

 179   2                  lcd_show_num(54, 12, g_system.alarm_level, 1, 12, 0);
 180   2                  lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "Motor St:");
 181   2                  lcd_show_num(54, 24, g_system.motor_state, 1, 12, 0);
 182   2                  break;
 183   2          }
 184   1          
 185   1          lcd_reflash_gram();
 186   1      }
 187          
 188          /*-----------------------------------------------------------------------------
 189           * 函数名称: Display_Update
 190           * 功能描述: 显示更新
 191           *----------------------------------------------------------------------------*/
 192          void Display_Update(void)
 193          {
 194   1          if(g_system.system_mode == MODE_NORMAL)
 195   1          {
 196   2              Display_Main_Screen();
 197   2          }
 198   1          else if(g_system.system_mode == MODE_SETTING)
 199   1          {
 200   2              Display_Menu();
 201   2          }
 202   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =   1309    ----
   CONSTANT SIZE    =    113    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----      12
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
