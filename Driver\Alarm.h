#ifndef __ALARM_H__
#define __ALARM_H__

#include "Config.h"

/*=============================================================================
 * 渐变报警模块头文件
 * 功能：蜂鸣器频率控制、LED闪烁控制、渐变式报警
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数声明
 *----------------------------------------------------------------------------*/
void Alarm_Init(void);                           // 报警模块初始化
void Buzzer_Control(unsigned char enable, unsigned int freq);  // 蜂鸣器控制
void LED_Control(unsigned char led_num, unsigned char state);  // LED控制
void Alarm_Process(void);                        // 报警处理主函数
unsigned char Calc_Alarm_Level(float deviation); // 计算报警等级
void Update_Alarm_Output(void);                  // 更新报警输出

#endif // __ALARM_H__
