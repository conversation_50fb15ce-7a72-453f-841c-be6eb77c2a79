/*=============================================================================
 * 智能温控保温杯系统主程序
 * 功能：电机温控、多传感器检测、LCD显示、渐变报警、串口通信
 *============================================================================*/
#define MAIN_C  // 定义全局变量

/*头文件声明区*/
#include <REG52.H>     // 单片机寄存器专用头文件
#include "Config.h"    // 系统配置文件
#include "Key.h"       // 按键驱动
#include "Delay.h"     // 延时函数
#include "LCD9648.h"   // LCD显示
#include "iic.h"       // IIC通信
#include "onewire.h"   // DS18B20温度传感器
#include "Motor.h"     // 电机控制
#include "Sensor.h"    // 传感器检测
#include "DS1302.h"    // 时钟模块
#include "Alarm.h"     // 报警模块
#include "Display.h"   // 显示扩展
#include "UART.h"      // 串口通信
#include "ADC.h"       // ADC芯片驱动
/*变量声明区*/
unsigned char Key_Val, Key_Up, Key_Down, Key_Old;  // 按键相关变量
unsigned char Key_Slow_Down;

// 任务调度计数器
unsigned int task_1ms_counter = 0;    // 1ms任务计数器
unsigned int task_10ms_counter = 0;   // 10ms任务计数器
unsigned int task_100ms_counter = 0;  // 100ms任务计数器
unsigned int task_500ms_counter = 0;  // 500ms任务计数器
unsigned int task_1000ms_counter = 0; // 1000ms任务计数器

// 时钟相关变量
Time_t current_time;
unsigned int software_timer_counter = 0;  // 软件计时器计数器


/*-----------------------------------------------------------------------------
 * 函数名称: Key_Process
 * 功能描述: 按键处理函数，支持温度设定和菜单操作
 *----------------------------------------------------------------------------*/
void Key_Process(void)
{
	unsigned char need_update = 0;  // 标记是否需要立即更新显示

	if(Key_Slow_Down) return;
	Key_Slow_Down = 1;

	Key_Val = Key_Read();
	Key_Down = Key_Val & (Key_Old ^ Key_Val);
	Key_Up = ~Key_Val & (Key_Old ^ Key_Val);
	Key_Old = Key_Val;

	// 按键处理
	switch(Key_Down)
	{
		case 1:  // KEY1 - 温度设定+ / 数值增加
			if(g_system.system_mode == MODE_SETTING)
			{
				if(g_system.menu_index == MENU_TEMP_SET)
				{
					g_system.target_temp += 1.0;
					if(g_system.target_temp > TEMP_MAX)
						g_system.target_temp = TEMP_MAX;
					need_update = 1;  // 需要立即更新显示
				}
			}
			break;

		case 2:  // KEY2 - 温度设定- / 数值减少
			if(g_system.system_mode == MODE_SETTING)
			{
				if(g_system.menu_index == MENU_TEMP_SET)
				{
					g_system.target_temp -= 1.0;
					if(g_system.target_temp < TEMP_MIN)
						g_system.target_temp = TEMP_MIN;
					need_update = 1;  // 需要立即更新显示
				}
			}
			break;

		case 3:  // KEY3 - 菜单切换
			if(g_system.system_mode == MODE_NORMAL)
			{
				g_system.system_mode = MODE_SETTING;
				g_system.menu_index = MENU_TEMP_SET;
			}
			else
			{
				g_system.menu_index++;
				if(g_system.menu_index > MENU_SYSTEM_INFO)
					g_system.menu_index = MENU_TEMP_SET;
			}
			need_update = 1;  // 菜单切换需要立即更新显示
			break;

		case 4:  // KEY4 - 确认设置 / 退出菜单
			if(g_system.system_mode == MODE_SETTING)
			{
				g_system.system_mode = MODE_NORMAL;
			}
			need_update = 1;  // 模式切换需要立即更新显示
			break;
	}

	// 如果有按键操作，立即更新显示
	if(need_update)
	{
		Display_Update();  // 立即刷新显示，解决切换过慢问题
	}
}

/*-----------------------------------------------------------------------------
 * 函数名称: System_Init
 * 功能描述: 系统初始化函数
 *----------------------------------------------------------------------------*/
void System_Init(void)
{
	// 初始化各个模块
	Motor_Init();      // 电机控制初始化
	Sensor_Init();     // 传感器初始化
	DS1302_Init();     // 时钟初始化
	Alarm_Init();      // 报警初始化
	Display_Init();    // 显示初始化
	UART_Init();       // 串口初始化

	// 初始化时钟（设置合理的初始时间）
	current_time.year = 24;    // 2024年
	current_time.month = 1;    // 1月
	current_time.day = 1;      // 1日
	current_time.hour = 12;    // 12时
	current_time.minute = 30;  // 30分
	current_time.second = 45;  // 45秒
	DS1302_Set_Time(&current_time);

	// 延时确保写入完成
	delay_ms(100);

	// 发送系统启动信息
	UART_Send_String("System Started!\r\n");
}

/*定时器0中断初始化函数*/
void Timer0Init(void)		//1毫秒@11.0592MHz
{
	TMOD &= 0xF0;		//设置定时器模式
	TMOD |= 0x01;		//设置定时器模式
	TL0 = 0x66;		//设置定时初值
	TH0 = 0xFC;		//设置定时初值
	TF0 = 0;		//清除TF0标志
	TR0 = 1;		//定时器0开始计时
	
	ET0 = 1;
	EA = 1;
}


/*-----------------------------------------------------------------------------
 * 函数名称: Temperature_Control
 * 功能描述: 温度控制算法
 *----------------------------------------------------------------------------*/
void Temperature_Control(void)
{
	float temp_diff;
	unsigned char motor_dir;
	unsigned char motor_speed;

	// 计算温度偏差
	temp_diff = g_system.current_temp - g_system.target_temp;

	// 温度控制逻辑
	if(temp_diff < -TEMP_DEADZONE)  // 当前温度低于设定值
	{
		motor_dir = MOTOR_FORWARD;  // 正转制热
		motor_speed = Motor_Calc_Speed(-temp_diff);
	}
	else if(temp_diff > TEMP_DEADZONE)  // 当前温度高于设定值
	{
		motor_dir = MOTOR_REVERSE;  // 反转制冷
		motor_speed = Motor_Calc_Speed(temp_diff);
	}
	else  // 在死区内
	{
		motor_dir = MOTOR_STOP;     // 停止
		motor_speed = 0;
	}

	// 控制电机
	Motor_Control(motor_dir, motor_speed);
}

/*-----------------------------------------------------------------------------
 * 函数名称: Timer0Server
 * 功能描述: 定时器0中断服务函数 - 多任务调度
 *----------------------------------------------------------------------------*/
void Timer0Server() interrupt 1
{
	// 1ms任务：按键扫描
	if(++Key_Slow_Down == 10) Key_Slow_Down = 0;

	// 任务调度计数器
	task_1ms_counter++;
	task_10ms_counter++;
	task_100ms_counter++;
	task_500ms_counter++;
	task_1000ms_counter++;
}


/*-----------------------------------------------------------------------------
 * 函数名称: main
 * 功能描述: 主函数 - 系统主循环
 *----------------------------------------------------------------------------*/
void main()
{
	// 系统初始化
	System_Init();
	Timer0Init();

	// 主循环
	while(1)
	{
		// 1ms任务：按键处理
		if(task_1ms_counter >= TASK_1MS_PERIOD)
		{
			task_1ms_counter = 0;
			Key_Process();
		}

		// 10ms任务：ADC数据采集
		if(task_10ms_counter >= TASK_10MS_PERIOD)
		{
			task_10ms_counter = 0;
			Sensor_Update_All();      // 更新传感器数据
			Motor_Get_Speed_Voltage(); // 读取电机转速电压
		}

		// 100ms任务：显示更新和温度读取
		if(task_100ms_counter >= TASK_100MS_PERIOD)
		{
			task_100ms_counter = 0;
			g_system.current_temp = rd_temperature();  // 读取DS18B20温度
			Display_Update();         // 更新显示
		}

		// 500ms任务：温度控制、报警处理和时钟读取
		if(task_500ms_counter >= TASK_500MS_PERIOD)
		{
			task_500ms_counter = 0;
			DS1302_Get_Time(&current_time);  // 提高时钟读取频率到500ms
			Temperature_Control();    // 温度控制算法
			Alarm_Process();          // 报警处理
		}

		// 1000ms任务：串口输出
		if(task_1000ms_counter >= TASK_1000MS_PERIOD)
		{
			task_1000ms_counter = 0;
			UART_Send_System_Data();  // 发送系统数据
			UART_Send_Alarm_Info();   // 发送报警信息
		}
	}
}