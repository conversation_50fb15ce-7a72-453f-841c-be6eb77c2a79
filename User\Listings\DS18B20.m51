<PERSON>L<PERSON> BANKED LINKER/LOCATER V6.22                                                        07/07/2025  09:58:07  PAGE 1


BL51 BANKED LINKER/LOCATER V6.22, INVOKED BY:
E:\KEIL\C51\BIN\BL51.EXE .\Objects\main.obj, .\Objects\Key.obj, .\Objects\iic.obj, .\Objects\LCD9648.obj, .\Objects\Dela
>> y.obj, .\Objects\onewire.obj, .\Objects\Motor.obj, .\Objects\Sensor.obj, .\Objects\DS1302.obj, .\Objects\Alarm.obj, .
>> \Objects\Display.obj, .\Objects\UART.obj, .\Objects\ADC.obj TO .\Objects\DS18B20 PRINT (.\Listings\DS18B20.m51) RAMSI
>> ZE (256)


MEMORY MODEL: SMALL WITH FLOATING POINT ARITHMETIC


INPUT MODULES INCLUDED:
  .\Objects\main.obj (MAIN)
  .\Objects\Key.obj (KEY)
  .\Objects\iic.obj (IIC)
  .\Objects\LCD9648.obj (LCD9648)
  .\Objects\Delay.obj (DELAY)
  .\Objects\onewire.obj (ONEWIRE)
  .\Objects\Motor.obj (MOTOR)
  .\Objects\Sensor.obj (SENSOR)
  .\Objects\DS1302.obj (DS1302)
  .\Objects\Alarm.obj (ALARM)
  .\Objects\Display.obj (DISPLAY)
  .\Objects\UART.obj (UART)
  .\Objects\ADC.obj (ADC)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPADD)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPMUL)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPDIV)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPCMP)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPNEG)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FCAST)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?CASTF)
  E:\KEIL\C51\LIB\C51FPS.LIB (?C?FPGETOPN)
  E:\KEIL\C51\LIB\C51S.LIB (?C_STARTUP)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CLDOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?CSTOPTR)
  E:\KEIL\C51\LIB\C51S.LIB (?C?IMUL)
  E:\KEIL\C51\LIB\C51S.LIB (?C?UIDIV)
  E:\KEIL\C51\LIB\C51S.LIB (?C?SIDIV)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LMUL)
  E:\KEIL\C51\LIB\C51S.LIB (?C?ULDIV)
  E:\KEIL\C51\LIB\C51S.LIB (?C?LNEG)
  E:\KEIL\C51\LIB\C51S.LIB (?C_INIT)


LINK MAP OF MODULE:  .\Objects\DS18B20 (MAIN)


            TYPE    BASE      LENGTH    RELOCATION   SEGMENT NAME
            -----------------------------------------------------

            * * * * * * *   D A T A   M E M O R Y   * * * * * * *
            REG     0000H     0008H     ABSOLUTE     "REG BANK 0"
            DATA    0008H     0006H     UNIT         ?DT?ALARM
            DATA    000EH     0003H     UNIT         ?DT?MOTOR
                    0011H     000FH                  *** GAP ***
            BIT     0020H.0   0000H.1   UNIT         _BIT_GROUP_
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 2


                    0020H.1   0000H.7                *** GAP ***
            DATA    0021H     0036H     UNIT         ?DT?MAIN
            DATA    0057H     0020H     UNIT         _DATA_GROUP_
            IDATA   0077H     0001H     UNIT         ?STACK

            * * * * * * *  X D A T A   M E M O R Y  * * * * * * *
            XDATA   0000H     0240H     UNIT         ?XD?LCD9648

            * * * * * * *   C O D E   M E M O R Y   * * * * * * *
            CODE    0000H     0003H     ABSOLUTE     
            CODE    0003H     0008H     UNIT         ?PR?_UART_SEND_BYTE?UART
            CODE    000BH     0003H     ABSOLUTE     
            CODE    000EH     000CH     UNIT         ?PR?I2CSTOP?IIC
                    001AH     0001H                  *** GAP ***
            CODE    001BH     0003H     ABSOLUTE     
            CODE    001EH     17C0H     UNIT         ?CO?LCD9648
            CODE    17DEH     0629H     UNIT         ?C?LIB_CODE
            CODE    1E07H     016BH     UNIT         ?PR?DISPLAY_MAIN_SCREEN?DISPLAY
            CODE    1F72H     0148H     UNIT         ?PR?_DISPLAY_FLOAT?DISPLAY
            CODE    20BAH     0147H     UNIT         ?PR?_UART_SEND_FLOAT?UART
            CODE    2201H     0140H     UNIT         ?PR?DISPLAY_MENU?DISPLAY
            CODE    2341H     0117H     UNIT         ?PR?_DS1302_GET_TIME?DS1302
            CODE    2458H     0114H     UNIT         ?PR?_DISPLAY_TIME?DISPLAY
            CODE    256CH     00F9H     UNIT         ?PR?KEY_PROCESS?MAIN
            CODE    2665H     00ECH     UNIT         ?PR?UART_SEND_SYSTEM_DATA?UART
            CODE    2751H     00DEH     UNIT         ?PR?_LCD_SHOW_CHAR?LCD9648
            CODE    282FH     00ADH     UNIT         ?PR?_LCD_SHOW_NUM?LCD9648
            CODE    28DCH     00A2H     UNIT         ?PR?ADC_GET_NTC_TEMPERATURE?ADC
            CODE    297EH     009FH     UNIT         ?PR?ADC_GET_CURRENT_DETECT?ADC
            CODE    2A1DH     008CH     UNIT         ?C_C51STARTUP
            CODE    2AA9H     008AH     UNIT         ?PR?_CALC_ALARM_LEVEL?ALARM
            CODE    2B33H     0083H     UNIT         ?PR?MAIN?MAIN
            CODE    2BB6H     0083H     UNIT         ?PR?ALARM_PROCESS?ALARM
            CODE    2C39H     007AH     UNIT         ?PR?_DS1302_READ_BYTE?DS1302
            CODE    2CB3H     0076H     UNIT         ?PR?UART_SEND_ALARM_INFO?UART
            CODE    2D29H     0073H     UNIT         ?PR?TEMPERATURE_CONTROL?MAIN
            CODE    2D9CH     0072H     UNIT         ?PR?_DS1302_WRITE_BYTE?DS1302
            CODE    2E0EH     0071H     UNIT         ?CO?DISPLAY
            CODE    2E7FH     0070H     UNIT         ?PR?UPDATE_ALARM_OUTPUT?ALARM
            CODE    2EEFH     006FH     UNIT         ?PR?_LCD_DRAW_DOT?LCD9648
            CODE    2F5EH     0069H     UNIT         ?CO?UART
            CODE    2FC7H     0067H     UNIT         ?PR?_LCD_SHOW_STRING?LCD9648
            CODE    302EH     0067H     UNIT         ?PR?_DS1302_SET_TIME?DS1302
            CODE    3095H     005FH     UNIT         ?PR?_MOTOR_CALC_SPEED?MOTOR
            CODE    30F4H     0051H     UNIT         ?C_INITSEG
            CODE    3145H     0051H     UNIT         ?PR?LCD9648_CLEAR?LCD9648
            CODE    3196H     0051H     UNIT         ?PR?_BUZZER_CONTROL?ALARM
            CODE    31E7H     0043H     UNIT         ?PR?LCD9648_INIT?LCD9648
            CODE    322AH     003DH     UNIT         ?PR?SYSTEM_INIT?MAIN
            CODE    3267H     003CH     UNIT         ?PR?ADC_GET_MOTOR_VOLTAGE?ADC
            CODE    32A3H     003BH     UNIT         ?PR?TIMER0SERVER?MAIN
            CODE    32DEH     003BH     UNIT         ?PR?TIMER1_PWM_ISR?MOTOR
            CODE    3319H     0039H     UNIT         ?PR?_LCD_POW?LCD9648
            CODE    3352H     0036H     UNIT         ?PR?LCD_REFLASH_GRAM?LCD9648
            CODE    3388H     0035H     UNIT         ?PR?RD_TEMPERATURE?ONEWIRE
            CODE    33BDH     0034H     UNIT         ?PR?_ADC_SPI_WRITE_BYTE?ADC
            CODE    33F1H     0031H     UNIT         ?PR?ADC_SPI_READ_BYTE?ADC
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 3


            CODE    3422H     002CH     UNIT         ?PR?_I2CSENDBYTE?IIC
            CODE    344EH     002CH     UNIT         ?PR?_ADC_READ_CHANNEL?ADC
            CODE    347AH     0027H     UNIT         ?PR?INIT_DS18B20?ONEWIRE
            CODE    34A1H     0025H     UNIT         ?PR?_AD_READ?IIC
            CODE    34C6H     001FH     UNIT         ?PR?_LED_CONTROL?ALARM
            CODE    34E5H     001EH     UNIT         ?PR?_DELAY_MS?DELAY
            CODE    3503H     001EH     UNIT         ?PR?_WRITE_DS18B20?ONEWIRE
            CODE    3521H     001DH     UNIT         ?PR?I2CRECEIVEBYTE?IIC
            CODE    353EH     001DH     UNIT         ?PR?I2CWAITACK?IIC
            CODE    355BH     001DH     UNIT         ?PR?_DECIMAL_TO_BCD?DS1302
            CODE    3578H     001CH     UNIT         ?PR?_MOTOR_CONTROL?MOTOR
            CODE    3594H     001AH     UNIT         ?PR?_I2C_DELAY?IIC
            CODE    35AEH     001AH     UNIT         ?PR?_DELAY_ONEWIRE?ONEWIRE
            CODE    35C8H     001AH     UNIT         ?PR?READ_DS18B20?ONEWIRE
            CODE    35E2H     0019H     UNIT         ?PR?_LCD9648_SPI_WRITE_BYTE?LCD9648
            CODE    35FBH     0018H     UNIT         ?PR?_I2CSENDACK?IIC
            CODE    3613H     0016H     UNIT         ?PR?KEY_READ?KEY
            CODE    3629H     0015H     UNIT         ?PR?TIMER0INIT?MAIN
            CODE    363EH     0015H     UNIT         ?PR?_DA_WRITE?IIC
            CODE    3653H     0014H     UNIT         ?PR?DS1302_INIT?DS1302
            CODE    3667H     0014H     UNIT         ?PR?ALARM_INIT?ALARM
            CODE    367BH     0013H     UNIT         ?PR?_UART_SEND_STRING?UART
            CODE    368EH     0012H     UNIT         ?CO?MAIN
            CODE    36A0H     0011H     UNIT         ?PR?TIMER1_PWM_INIT?MOTOR
            CODE    36B1H     0011H     UNIT         ?PR?UART_INIT?UART
            CODE    36C2H     0010H     UNIT         ?PR?_LCD9648_WRITE_CMD?LCD9648
            CODE    36D2H     0010H     UNIT         ?PR?DISPLAY_UPDATE?DISPLAY
            CODE    36E2H     000FH     UNIT         ?PR?I2CSTART?IIC
            CODE    36F1H     000FH     UNIT         ?PR?_BCD_TO_DECIMAL?DS1302
            CODE    3700H     000EH     UNIT         ?PR?MOTOR_INIT?MOTOR
            CODE    370EH     000BH     UNIT         ?PR?_DELAY_10US?DELAY
            CODE    3719H     000AH     UNIT         ?PR?_LCD9648_WRITE_DAT?LCD9648
            CODE    3723H     0009H     UNIT         ?PR?SENSOR_UPDATE_ALL?SENSOR
            CODE    372CH     0007H     UNIT         ?PR?ADC_INIT?ADC
            CODE    3733H     0006H     UNIT         ?PR?MOTOR_STOP?MOTOR
            CODE    3739H     0006H     UNIT         ?PR?DISPLAY_INIT?DISPLAY
            CODE    373FH     0004H     UNIT         ?PR?MOTOR_GET_SPEED_VOLTAGE?MOTOR
            CODE    3743H     0004H     UNIT         ?PR?GET_NTC_TEMPERATURE?SENSOR
            CODE    3747H     0004H     UNIT         ?PR?GET_CURRENT_DETECT?SENSOR
            CODE    374BH     0003H     UNIT         ?PR?SENSOR_INIT?SENSOR



OVERLAY MAP OF MODULE:   .\Objects\DS18B20 (MAIN)


SEGMENT                                        BIT_GROUP          DATA_GROUP 
  +--> CALLED SEGMENT                       START    LENGTH     START    LENGTH
-------------------------------------------------------------------------------
?C_C51STARTUP                               -----    -----      -----    -----
  +--> ?PR?MAIN?MAIN
  +--> ?C_INITSEG

?PR?MAIN?MAIN                               -----    -----      -----    -----
  +--> ?PR?SYSTEM_INIT?MAIN
  +--> ?PR?TIMER0INIT?MAIN
  +--> ?PR?KEY_PROCESS?MAIN
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 4


  +--> ?PR?SENSOR_UPDATE_ALL?SENSOR
  +--> ?PR?MOTOR_GET_SPEED_VOLTAGE?MOTOR
  +--> ?PR?RD_TEMPERATURE?ONEWIRE
  +--> ?PR?DISPLAY_UPDATE?DISPLAY
  +--> ?PR?_DS1302_GET_TIME?DS1302
  +--> ?PR?TEMPERATURE_CONTROL?MAIN
  +--> ?PR?ALARM_PROCESS?ALARM
  +--> ?PR?UART_SEND_SYSTEM_DATA?UART
  +--> ?PR?UART_SEND_ALARM_INFO?UART

?PR?SYSTEM_INIT?MAIN                        -----    -----      -----    -----
  +--> ?PR?MOTOR_INIT?MOTOR
  +--> ?PR?SENSOR_INIT?SENSOR
  +--> ?PR?DS1302_INIT?DS1302
  +--> ?PR?ALARM_INIT?ALARM
  +--> ?PR?DISPLAY_INIT?DISPLAY
  +--> ?PR?UART_INIT?UART
  +--> ?PR?_DS1302_SET_TIME?DS1302
  +--> ?PR?_DELAY_MS?DELAY
  +--> ?CO?MAIN
  +--> ?PR?_UART_SEND_STRING?UART

?PR?MOTOR_INIT?MOTOR                        -----    -----      -----    -----
  +--> ?PR?TIMER1_PWM_INIT?MOTOR

?PR?SENSOR_INIT?SENSOR                      -----    -----      -----    -----
  +--> ?PR?ADC_INIT?ADC

?PR?DS1302_INIT?DS1302                      -----    -----      -----    -----
  +--> ?PR?_DS1302_WRITE_BYTE?DS1302

?PR?_DS1302_WRITE_BYTE?DS1302               -----    -----      005AH    0003H
  +--> ?PR?_DELAY_10US?DELAY

?PR?DISPLAY_INIT?DISPLAY                    -----    -----      -----    -----
  +--> ?PR?LCD9648_INIT?LCD9648
  +--> ?PR?LCD9648_CLEAR?LCD9648

?PR?LCD9648_INIT?LCD9648                    -----    -----      -----    -----
  +--> ?PR?_DELAY_MS?DELAY
  +--> ?PR?_LCD9648_WRITE_CMD?LCD9648

?PR?_LCD9648_WRITE_CMD?LCD9648              -----    -----      -----    -----
  +--> ?PR?_LCD9648_SPI_WRITE_BYTE?LCD9648

?PR?LCD9648_CLEAR?LCD9648                   -----    -----      0058H    0001H
  +--> ?PR?_LCD9648_WRITE_CMD?LCD9648
  +--> ?PR?_LCD9648_WRITE_DAT?LCD9648
  +--> ?PR?LCD_REFLASH_GRAM?LCD9648

?PR?_LCD9648_WRITE_DAT?LCD9648              -----    -----      -----    -----
  +--> ?PR?_LCD9648_SPI_WRITE_BYTE?LCD9648

?PR?LCD_REFLASH_GRAM?LCD9648                -----    -----      -----    -----
  +--> ?PR?_LCD9648_WRITE_CMD?LCD9648
  +--> ?PR?_LCD9648_WRITE_DAT?LCD9648

BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 5


?PR?_DS1302_SET_TIME?DS1302                 -----    -----      0057H    0003H
  +--> ?PR?_DECIMAL_TO_BCD?DS1302
  +--> ?PR?_DS1302_WRITE_BYTE?DS1302

?PR?_UART_SEND_STRING?UART                  -----    -----      -----    -----
  +--> ?PR?_UART_SEND_BYTE?UART

?PR?KEY_PROCESS?MAIN                        -----    -----      0057H    0001H
  +--> ?PR?KEY_READ?KEY
  +--> ?PR?DISPLAY_UPDATE?DISPLAY

?PR?DISPLAY_UPDATE?DISPLAY                  -----    -----      -----    -----
  +--> ?PR?DISPLAY_MAIN_SCREEN?DISPLAY
  +--> ?PR?DISPLAY_MENU?DISPLAY

?PR?DISPLAY_MAIN_SCREEN?DISPLAY             -----    -----      -----    -----
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?DISPLAY
  +--> ?PR?_LCD_SHOW_STRING?LCD9648
  +--> ?PR?_DISPLAY_FLOAT?DISPLAY
  +--> ?PR?_LCD_SHOW_NUM?LCD9648
  +--> ?PR?_DISPLAY_TIME?DISPLAY
  +--> ?PR?LCD_REFLASH_GRAM?LCD9648

?PR?_LCD_SHOW_STRING?LCD9648                -----    -----      0058H    0009H
  +--> ?PR?_LCD_SHOW_CHAR?LCD9648

?PR?_LCD_SHOW_CHAR?LCD9648                  -----    -----      006EH    0009H
  +--> ?CO?LCD9648
  +--> ?PR?_LCD_DRAW_DOT?LCD9648

?PR?_DISPLAY_FLOAT?DISPLAY                  -----    -----      0058H    000AH
  +--> ?PR?_LCD_SHOW_CHAR?LCD9648
  +--> ?PR?_LCD_SHOW_NUM?LCD9648

?PR?_LCD_SHOW_NUM?LCD9648                   -----    -----      0062H    000CH
  +--> ?PR?_LCD_POW?LCD9648
  +--> ?PR?_LCD_SHOW_CHAR?LCD9648

?PR?_LCD_POW?LCD9648                        -----    -----      006EH    0006H

?PR?_DISPLAY_TIME?DISPLAY                   -----    -----      0058H    0002H
  +--> ?PR?_LCD_SHOW_CHAR?LCD9648
  +--> ?PR?_LCD_SHOW_NUM?LCD9648

?PR?DISPLAY_MENU?DISPLAY                    -----    -----      -----    -----
  +--> ?PR?LCD9648_CLEAR?LCD9648
  +--> ?CO?DISPLAY
  +--> ?PR?_LCD_SHOW_STRING?LCD9648
  +--> ?PR?_DISPLAY_FLOAT?DISPLAY
  +--> ?PR?_DISPLAY_TIME?DISPLAY
  +--> ?PR?_LCD_SHOW_NUM?LCD9648
  +--> ?PR?LCD_REFLASH_GRAM?LCD9648

?PR?SENSOR_UPDATE_ALL?SENSOR                -----    -----      -----    -----
  +--> ?PR?GET_NTC_TEMPERATURE?SENSOR
  +--> ?PR?GET_CURRENT_DETECT?SENSOR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 6


  +--> ?PR?ADC_GET_MOTOR_VOLTAGE?ADC

?PR?GET_NTC_TEMPERATURE?SENSOR              -----    -----      -----    -----
  +--> ?PR?ADC_GET_NTC_TEMPERATURE?ADC

?PR?ADC_GET_NTC_TEMPERATURE?ADC             -----    -----      0057H    0008H
  +--> ?PR?_ADC_READ_CHANNEL?ADC

?PR?_ADC_READ_CHANNEL?ADC                   -----    -----      005FH    0002H
  +--> ?PR?_DELAY_10US?DELAY
  +--> ?PR?_ADC_SPI_WRITE_BYTE?ADC
  +--> ?PR?ADC_SPI_READ_BYTE?ADC

?PR?_ADC_SPI_WRITE_BYTE?ADC                 -----    -----      0061H    0002H
  +--> ?PR?_DELAY_10US?DELAY

?PR?ADC_SPI_READ_BYTE?ADC                   -----    -----      0061H    0002H
  +--> ?PR?_DELAY_10US?DELAY

?PR?GET_CURRENT_DETECT?SENSOR               -----    -----      -----    -----
  +--> ?PR?ADC_GET_CURRENT_DETECT?ADC

?PR?ADC_GET_CURRENT_DETECT?ADC              -----    -----      0057H    0008H
  +--> ?PR?_ADC_READ_CHANNEL?ADC

?PR?ADC_GET_MOTOR_VOLTAGE?ADC               -----    -----      0057H    0004H
  +--> ?PR?_ADC_READ_CHANNEL?ADC

?PR?MOTOR_GET_SPEED_VOLTAGE?MOTOR           -----    -----      -----    -----
  +--> ?PR?ADC_GET_MOTOR_VOLTAGE?ADC

?PR?RD_TEMPERATURE?ONEWIRE                  -----    -----      -----    -----
  +--> ?PR?INIT_DS18B20?ONEWIRE
  +--> ?PR?_WRITE_DS18B20?ONEWIRE
  +--> ?PR?READ_DS18B20?ONEWIRE

?PR?INIT_DS18B20?ONEWIRE                    0020H.0  0000H.1    -----    -----
  +--> ?PR?_DELAY_ONEWIRE?ONEWIRE

?PR?_WRITE_DS18B20?ONEWIRE                  -----    -----      -----    -----
  +--> ?PR?_DELAY_ONEWIRE?ONEWIRE

?PR?READ_DS18B20?ONEWIRE                    -----    -----      -----    -----
  +--> ?PR?_DELAY_ONEWIRE?ONEWIRE

?PR?_DS1302_GET_TIME?DS1302                 -----    -----      0057H    0006H
  +--> ?PR?_DS1302_READ_BYTE?DS1302
  +--> ?PR?_BCD_TO_DECIMAL?DS1302

?PR?_DS1302_READ_BYTE?DS1302                -----    -----      005DH    0003H
  +--> ?PR?_DELAY_10US?DELAY

?PR?TEMPERATURE_CONTROL?MAIN                -----    -----      0057H    0006H
  +--> ?PR?_MOTOR_CALC_SPEED?MOTOR
  +--> ?PR?_MOTOR_CONTROL?MOTOR

?PR?_MOTOR_CALC_SPEED?MOTOR                 -----    -----      005DH    0008H
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 7



?PR?_MOTOR_CONTROL?MOTOR                    -----    -----      -----    -----
  +--> ?PR?_DA_WRITE?IIC

?PR?_DA_WRITE?IIC                           -----    -----      -----    -----
  +--> ?PR?I2CSTART?IIC
  +--> ?PR?_I2CSENDBYTE?IIC
  +--> ?PR?I2CWAITACK?IIC
  +--> ?PR?I2CSTOP?IIC

?PR?I2CSTART?IIC                            -----    -----      -----    -----
  +--> ?PR?_I2C_DELAY?IIC

?PR?_I2CSENDBYTE?IIC                        -----    -----      -----    -----
  +--> ?PR?_I2C_DELAY?IIC

?PR?I2CWAITACK?IIC                          -----    -----      -----    -----
  +--> ?PR?_I2CSENDBYTE?IIC
  +--> ?PR?_I2C_DELAY?IIC

?PR?I2CSTOP?IIC                             -----    -----      -----    -----
  +--> ?PR?_I2C_DELAY?IIC

?PR?ALARM_PROCESS?ALARM                     -----    -----      0057H    0006H
  +--> ?PR?_CALC_ALARM_LEVEL?ALARM
  +--> ?PR?_BUZZER_CONTROL?ALARM
  +--> ?PR?UPDATE_ALARM_OUTPUT?ALARM

?PR?_CALC_ALARM_LEVEL?ALARM                 -----    -----      005DH    0005H

?PR?_BUZZER_CONTROL?ALARM                   -----    -----      005DH    0002H

?PR?UART_SEND_SYSTEM_DATA?UART              -----    -----      -----    -----
  +--> ?CO?UART
  +--> ?PR?_UART_SEND_STRING?UART
  +--> ?PR?_UART_SEND_FLOAT?UART
  +--> ?PR?_UART_SEND_BYTE?UART

?PR?_UART_SEND_FLOAT?UART                   -----    -----      005BH    0016H
  +--> ?PR?_UART_SEND_BYTE?UART

?PR?UART_SEND_ALARM_INFO?UART               -----    -----      0057H    0004H
  +--> ?CO?UART
  +--> ?PR?_UART_SEND_STRING?UART
  +--> ?PR?_UART_SEND_FLOAT?UART



SYMBOL TABLE OF MODULE:  .\Objects\DS18B20 (MAIN)

  VALUE           TYPE          NAME
  ----------------------------------

  -------         MODULE        MAIN
  C:0000H         SYMBOL        _ICE_DUMMY_
  C:322AH         PUBLIC        System_Init
  D:0080H         PUBLIC        P0
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 8


  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  B:00A8H.7       PUBLIC        EA
  D:0021H         PUBLIC        task_1ms_counter
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:0023H         PUBLIC        current_time
  D:0029H         PUBLIC        task_1000ms_counter
  D:002BH         PUBLIC        Key_Down
  C:2B33H         PUBLIC        main
  D:002CH         PUBLIC        Key_Old
  D:002DH         PUBLIC        Key_Slow_Down
  D:002EH         PUBLIC        Key_Val
  C:32A3H         PUBLIC        Timer0Server
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:002FH         PUBLIC        task_10ms_counter
  B:00A8H.1       PUBLIC        ET0
  B:0088H.5       PUBLIC        TF0
  C:3629H         PUBLIC        Timer0Init
  D:008CH         PUBLIC        TH0
  D:0031H         PUBLIC        Key_Up
  D:008AH         PUBLIC        TL0
  B:0088H.4       PUBLIC        TR0
  D:0032H         PUBLIC        software_timer_counter
  C:2D29H         PUBLIC        Temperature_Control
  D:00C8H         PUBLIC        T2CON
  C:256CH         PUBLIC        Key_Process
  D:0034H         PUBLIC        task_100ms_counter
  D:0036H         PUBLIC        g_system
  D:0055H         PUBLIC        task_500ms_counter
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_PROCESS
  -------         DO            
  D:0057H         SYMBOL        need_update
  -------         ENDDO         
  C:256CH         LINE#         42
  C:256CH         LINE#         43
  C:256CH         LINE#         44
  C:256FH         LINE#         46
  C:2576H         LINE#         47
  C:2579H         LINE#         49
  C:257EH         LINE#         50
  C:2587H         LINE#         51
  C:258DH         LINE#         52
  C:2590H         LINE#         55
  C:25A9H         LINE#         56
  C:25A9H         LINE#         57
  C:25A9H         LINE#         58
  C:25B2H         LINE#         59
  C:25B2H         LINE#         60
  C:25B9H         LINE#         61
  C:25B9H         LINE#         62
  C:25D2H         LINE#         63
  C:25E8H         LINE#         64
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 9


  C:25F3H         LINE#         65
  C:25F3H         LINE#         66
  C:25F3H         LINE#         67
  C:25F3H         LINE#         68
  C:25F5H         LINE#         70
  C:25F5H         LINE#         71
  C:25FBH         LINE#         72
  C:25FBH         LINE#         73
  C:25FFH         LINE#         74
  C:25FFH         LINE#         75
  C:2618H         LINE#         76
  C:262CH         LINE#         77
  C:2637H         LINE#         78
  C:2637H         LINE#         79
  C:2637H         LINE#         80
  C:2637H         LINE#         81
  C:2639H         LINE#         83
  C:2639H         LINE#         84
  C:263DH         LINE#         85
  C:263DH         LINE#         86
  C:2640H         LINE#         87
  C:2642H         LINE#         88
  C:2644H         LINE#         90
  C:2644H         LINE#         91
  C:2646H         LINE#         92
  C:264DH         LINE#         93
  C:2650H         LINE#         94
  C:2650H         LINE#         95
  C:2650H         LINE#         96
  C:2652H         LINE#         98
  C:2652H         LINE#         99
  C:2657H         LINE#         100
  C:2657H         LINE#         101
  C:265AH         LINE#         102
  C:265AH         LINE#         103
  C:265DH         LINE#         104
  C:265DH         LINE#         105
  C:265DH         LINE#         108
  C:2661H         LINE#         109
  C:2661H         LINE#         110
  C:2664H         LINE#         111
  C:2664H         LINE#         112
  -------         ENDPROC       KEY_PROCESS
  -------         PROC          SYSTEM_INIT
  C:322AH         LINE#         118
  C:322AH         LINE#         119
  C:322AH         LINE#         121
  C:322DH         LINE#         122
  C:3230H         LINE#         123
  C:3233H         LINE#         124
  C:3236H         LINE#         125
  C:3239H         LINE#         126
  C:323CH         LINE#         129
  C:323FH         LINE#         130
  C:3242H         LINE#         131
  C:3245H         LINE#         132
  C:3248H         LINE#         133
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 10


  C:324BH         LINE#         134
  C:324EH         LINE#         135
  C:3257H         LINE#         138
  C:325EH         LINE#         141
  -------         ENDPROC       SYSTEM_INIT
  -------         PROC          TIMER0INIT
  C:3629H         LINE#         145
  C:3629H         LINE#         146
  C:3629H         LINE#         147
  C:362CH         LINE#         148
  C:362FH         LINE#         149
  C:3632H         LINE#         150
  C:3635H         LINE#         151
  C:3637H         LINE#         152
  C:3639H         LINE#         154
  C:363BH         LINE#         155
  C:363DH         LINE#         156
  -------         ENDPROC       TIMER0INIT
  -------         PROC          TEMPERATURE_CONTROL
  -------         DO            
  D:0057H         SYMBOL        temp_diff
  D:005BH         SYMBOL        motor_dir
  D:005CH         SYMBOL        motor_speed
  -------         ENDDO         
  C:2D29H         LINE#         163
  C:2D29H         LINE#         164
  C:2D29H         LINE#         170
  C:2D44H         LINE#         173
  C:2D58H         LINE#         174
  C:2D58H         LINE#         175
  C:2D5BH         LINE#         176
  C:2D66H         LINE#         177
  C:2D68H         LINE#         178
  C:2D7EH         LINE#         179
  C:2D7EH         LINE#         180
  C:2D81H         LINE#         181
  C:2D8EH         LINE#         182
  C:2D90H         LINE#         184
  C:2D90H         LINE#         185
  C:2D93H         LINE#         186
  C:2D95H         LINE#         187
  C:2D95H         LINE#         190
  -------         ENDPROC       TEMPERATURE_CONTROL
  -------         PROC          TIMER0SERVER
  C:32A3H         LINE#         197
  C:32A7H         LINE#         200
  C:32B1H         LINE#         203
  C:32B9H         LINE#         204
  C:32C1H         LINE#         205
  C:32C9H         LINE#         206
  C:32D1H         LINE#         207
  C:32D9H         LINE#         208
  -------         ENDPROC       TIMER0SERVER
  -------         PROC          MAIN
  C:2B33H         LINE#         215
  C:2B33H         LINE#         216
  C:2B33H         LINE#         218
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 11


  C:2B36H         LINE#         219
  C:2B39H         LINE#         222
  C:2B39H         LINE#         223
  C:2B39H         LINE#         225
  C:2B44H         LINE#         226
  C:2B44H         LINE#         227
  C:2B49H         LINE#         228
  C:2B4CH         LINE#         229
  C:2B4CH         LINE#         232
  C:2B57H         LINE#         233
  C:2B57H         LINE#         234
  C:2B5CH         LINE#         235
  C:2B5FH         LINE#         236
  C:2B62H         LINE#         237
  C:2B62H         LINE#         240
  C:2B6DH         LINE#         241
  C:2B6DH         LINE#         242
  C:2B72H         LINE#         243
  C:2B7DH         LINE#         244
  C:2B80H         LINE#         245
  C:2B80H         LINE#         248
  C:2B8BH         LINE#         249
  C:2B8BH         LINE#         250
  C:2B90H         LINE#         251
  C:2B98H         LINE#         252
  C:2B9BH         LINE#         253
  C:2B9EH         LINE#         254
  C:2B9EH         LINE#         257
  C:2BA9H         LINE#         258
  C:2BA9H         LINE#         259
  C:2BAEH         LINE#         260
  C:2BB1H         LINE#         261
  C:2BB4H         LINE#         262
  C:2BB4H         LINE#         263
  -------         ENDPROC       MAIN
  -------         ENDMOD        MAIN

  -------         MODULE        KEY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:3613H         PUBLIC        Key_Read
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  B:0090H.0       PUBLIC        KEY1
  B:0090H.1       PUBLIC        KEY2
  B:0090H.2       PUBLIC        KEY3
  B:0090H.3       PUBLIC        KEY4
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          KEY_READ
  -------         DO            
  D:0007H         SYMBOL        temp
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 12


  -------         ENDDO         
  C:3613H         LINE#         4
  C:3613H         LINE#         5
  C:3613H         LINE#         6
  C:3615H         LINE#         8
  C:3619H         LINE#         9
  C:361EH         LINE#         10
  C:3623H         LINE#         11
  C:3628H         LINE#         13
  C:3628H         LINE#         14
  -------         ENDPROC       KEY_READ
  -------         ENDMOD        KEY

  -------         MODULE        IIC
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:34A1H         PUBLIC        _Ad_Read
  D:00A8H         PUBLIC        IE
  C:3598H         SYMBOL        _I2C_Delay
  C:363EH         PUBLIC        _Da_Write
  D:00B8H         PUBLIC        IP
  C:3521H         PUBLIC        I2CReceiveByte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:00A0H.1       PUBLIC        sda
  B:00A0H.0       PUBLIC        scl
  C:36E2H         PUBLIC        I2CStart
  C:35FBH         PUBLIC        _I2CSendAck
  C:3422H         PUBLIC        _I2CSendByte
  C:3549H         PUBLIC        I2CWaitAck
  C:000EH         PUBLIC        I2CStop
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  C:3594H         SYMBOL        L?0024
  -------         PROC          L?0023
  -------         ENDPROC       L?0023
  C:3594H         SYMBOL        L?0024
  -------         PROC          _I2C_DELAY
  D:0007H         SYMBOL        n
  C:3598H         LINE#         15
  C:3598H         LINE#         16
  C:3598H         LINE#         18
  C:3598H         LINE#         19
  C:359DH         LINE#         20
  C:35A2H         LINE#         21
  C:35A7H         LINE#         22
  C:35A7H         LINE#         23
  C:35ADH         LINE#         24
  -------         ENDPROC       _I2C_DELAY
  -------         PROC          I2CSTART
  C:36E2H         LINE#         27
  C:36E2H         LINE#         28
  C:36E2H         LINE#         29
  C:36E4H         LINE#         30
  C:36E4H         LINE#         31
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 13


  C:36E7H         LINE#         32
  C:36E9H         LINE#         33
  C:36EEH         LINE#         34
  C:36F0H         LINE#         35
  -------         ENDPROC       I2CSTART
  -------         PROC          I2CSTOP
  C:000EH         LINE#         38
  C:000EH         LINE#         39
  C:000EH         LINE#         40
  C:0010H         LINE#         41
  C:0010H         LINE#         42
  C:0013H         LINE#         43
  C:0015H         LINE#         44
  -------         ENDPROC       I2CSTOP
  -------         PROC          _I2CSENDBYTE
  D:0005H         SYMBOL        byt
  -------         DO            
  D:0004H         SYMBOL        i
  -------         ENDDO         
  C:3422H         LINE#         48
  C:3424H         LINE#         49
  C:3424H         LINE#         52
  C:3426H         LINE#         53
  C:3428H         LINE#         54
  C:342DH         LINE#         55
  C:3431H         LINE#         56
  C:3433H         LINE#         57
  C:3435H         LINE#         58
  C:3435H         LINE#         59
  C:3437H         LINE#         60
  C:3437H         LINE#         61
  C:343CH         LINE#         62
  C:343EH         LINE#         63
  C:3442H         LINE#         64
  C:3447H         LINE#         65
  C:344BH         LINE#         67
  C:344DH         LINE#         68
  -------         ENDPROC       _I2CSENDBYTE
  -------         PROC          I2CRECEIVEBYTE
  -------         DO            
  D:0005H         SYMBOL        da
  D:0004H         SYMBOL        i
  -------         ENDDO         
  C:3521H         LINE#         71
  C:3521H         LINE#         72
  C:3521H         LINE#         75
  C:3523H         LINE#         76
  C:3523H         LINE#         77
  C:3526H         LINE#         78
  C:352AH         LINE#         79
  C:352DH         LINE#         80
  C:3530H         LINE#         81
  C:3532H         LINE#         82
  C:3537H         LINE#         83
  C:353BH         LINE#         84
  C:353DH         LINE#         85
  -------         ENDPROC       I2CRECEIVEBYTE
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 14


  -------         PROC          L?0022
  -------         ENDPROC       L?0022
  -------         PROC          I2CWAITACK
  -------         DO            
  D:0005H         SYMBOL        ackbit
  -------         ENDDO         
  C:3549H         LINE#         88
  C:3549H         LINE#         89
  C:3549H         LINE#         92
  C:3549H         LINE#         93
  C:354CH         LINE#         94
  C:3551H         LINE#         95
  C:3553H         LINE#         96
  C:3558H         LINE#         98
  C:355AH         LINE#         99
  -------         ENDPROC       I2CWAITACK
  -------         PROC          _I2CSENDACK
  D:0007H         SYMBOL        ackbit
  C:35FBH         LINE#         102
  C:35FBH         LINE#         103
  C:35FBH         LINE#         104
  C:35FDH         LINE#         105
  C:3602H         LINE#         106
  C:3607H         LINE#         107
  C:3607H         LINE#         108
  C:360AH         LINE#         109
  C:360CH         LINE#         110
  C:360EH         LINE#         111
  -------         ENDPROC       _I2CSENDACK
  -------         PROC          _AD_READ
  D:0003H         SYMBOL        addr
  -------         DO            
  D:0005H         SYMBOL        temp
  -------         ENDDO         
  C:34A1H         LINE#         115
  C:34A3H         LINE#         116
  C:34A3H         LINE#         118
  C:34A6H         LINE#         119
  C:34A8H         LINE#         120
  C:34A8H         LINE#         121
  C:34A8H         LINE#         122
  C:34ABH         LINE#         124
  C:34AEH         LINE#         125
  C:34B3H         LINE#         126
  C:34B6H         LINE#         127
  C:34BBH         LINE#         128
  C:34C0H         LINE#         129
  C:34C3H         LINE#         131
  C:34C5H         LINE#         132
  -------         ENDPROC       _AD_READ
  -------         PROC          _DA_WRITE
  D:0003H         SYMBOL        addr
  C:363EH         LINE#         135
  C:3640H         LINE#         136
  C:3640H         LINE#         137
  C:3643H         LINE#         138
  C:3648H         LINE#         139
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 15


  C:364BH         LINE#         140
  C:364DH         LINE#         141
  C:364DH         LINE#         142
  C:364DH         LINE#         143
  C:3650H         LINE#         144
  -------         ENDPROC       _DA_WRITE
  -------         ENDMOD        IIC

  -------         MODULE        LCD9648
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:3352H         PUBLIC        lcd_reflash_gram
  C:282FH         PUBLIC        _lcd_show_num
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  X:0000H         PUBLIC        gdata_buf
  B:0080H.4       PUBLIC        RS
  C:31E7H         PUBLIC        lcd9648_init
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:36C8H         PUBLIC        _lcd9648_write_cmd
  C:0A82H         PUBLIC        ascii_2412
  C:001EH         PUBLIC        ascii_1206
  C:3719H         PUBLIC        _lcd9648_write_dat
  C:0492H         PUBLIC        ascii_1608
  B:0080H.3       PUBLIC        CS0
  C:2FC7H         PUBLIC        _lcd_show_string
  B:0080H.5       PUBLIC        SDA
  C:2751H         PUBLIC        _lcd_show_char
  B:0080H.6       PUBLIC        SCL
  C:35E2H         PUBLIC        _lcd9648_spi_write_byte
  D:00C8H         PUBLIC        T2CON
  C:3319H         PUBLIC        _lcd_pow
  C:2EEFH         PUBLIC        _lcd_draw_dot
  B:0080H.2       PUBLIC        RST
  D:00D0H         PUBLIC        PSW
  C:3145H         PUBLIC        lcd9648_clear
  -------         PROC          _LCD9648_SPI_WRITE_BYTE
  D:0007H         SYMBOL        dat
  -------         DO            
  D:0006H         SYMBOL        i
  -------         ENDDO         
  C:35E2H         LINE#         14
  C:35E2H         LINE#         15
  C:35E2H         LINE#         16
  C:35E4H         LINE#         18
  C:35E4H         LINE#         19
  C:35E4H         LINE#         20
  C:35E8H         LINE#         21
  C:35ECH         LINE#         23
  C:35EEH         LINE#         24
  C:35F2H         LINE#         25
  C:35F4H         LINE#         26
  C:35F6H         LINE#         27
  C:35FAH         LINE#         28
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 16


  -------         ENDPROC       _LCD9648_SPI_WRITE_BYTE
  C:36C3H         SYMBOL        L?0084
  -------         PROC          L?0083
  -------         ENDPROC       L?0083
  C:36C3H         SYMBOL        L?0084
  -------         PROC          _LCD9648_WRITE_CMD
  D:0007H         SYMBOL        cmd
  C:36C8H         LINE#         36
  C:36C8H         LINE#         37
  C:36C8H         LINE#         38
  C:36CAH         LINE#         39
  C:36CCH         LINE#         40
  C:36CFH         LINE#         41
  C:36D1H         LINE#         42
  -------         ENDPROC       _LCD9648_WRITE_CMD
  -------         PROC          _LCD9648_WRITE_DAT
  D:0007H         SYMBOL        dat
  C:3719H         LINE#         50
  C:3719H         LINE#         51
  C:3719H         LINE#         52
  C:371BH         LINE#         53
  C:371DH         LINE#         54
  C:3720H         LINE#         55
  C:3722H         LINE#         56
  -------         ENDPROC       _LCD9648_WRITE_DAT
  -------         PROC          LCD9648_INIT
  C:31E7H         LINE#         64
  C:31E7H         LINE#         65
  C:31E7H         LINE#         66
  C:31E9H         LINE#         67
  C:31F0H         LINE#         68
  C:31F2H         LINE#         69
  C:31F9H         LINE#         70
  C:31FBH         LINE#         71
  C:3202H         LINE#         73
  C:3207H         LINE#         74
  C:320CH         LINE#         75
  C:3211H         LINE#         76
  C:3216H         LINE#         77
  C:321BH         LINE#         78
  C:321DH         LINE#         79
  C:3220H         LINE#         80
  C:3225H         LINE#         81
  -------         ENDPROC       LCD9648_INIT
  -------         PROC          LCD9648_CLEAR
  -------         DO            
  D:0005H         SYMBOL        i
  D:0058H         SYMBOL        j
  -------         ENDDO         
  C:3145H         LINE#         90
  C:3145H         LINE#         91
  C:3145H         LINE#         94
  C:3147H         LINE#         95
  C:3147H         LINE#         97
  C:314AH         LINE#         98
  C:314DH         LINE#         99
  C:3152H         LINE#         101
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 17


  C:3155H         LINE#         102
  C:3155H         LINE#         103
  C:315AH         LINE#         104
  C:3163H         LINE#         105
  C:3167H         LINE#         106
  C:3169H         LINE#         107
  C:3169H         LINE#         108
  C:316CH         LINE#         109
  C:316CH         LINE#         110
  C:3188H         LINE#         111
  C:318FH         LINE#         112
  C:3193H         LINE#         113
  -------         ENDPROC       LCD9648_CLEAR
  -------         PROC          LCD_REFLASH_GRAM
  -------         DO            
  D:0005H         SYMBOL        i
  D:0004H         SYMBOL        n
  -------         ENDDO         
  C:3352H         LINE#         116
  C:3352H         LINE#         117
  C:3352H         LINE#         119
  C:3354H         LINE#         120
  C:3354H         LINE#         121
  C:335BH         LINE#         122
  C:335CH         LINE#         123
  C:335FH         LINE#         124
  C:3383H         LINE#         125
  C:3387H         LINE#         126
  -------         ENDPROC       LCD_REFLASH_GRAM
  -------         PROC          _LCD_DRAW_DOT
  D:0007H         SYMBOL        x
  D:0005H         SYMBOL        y
  D:0003H         SYMBOL        sta
  -------         DO            
  D:0006H         SYMBOL        posy1
  D:0005H         SYMBOL        posy2
  -------         ENDDO         
  C:2EEFH         LINE#         131
  C:2EEFH         LINE#         132
  C:2EEFH         LINE#         135
  C:2EFBH         LINE#         136
  C:2F02H         LINE#         137
  C:2F06H         LINE#         139
  C:2F09H         LINE#         140
  C:2F32H         LINE#         142
  C:2F5DH         LINE#         143
  -------         ENDPROC       _LCD_DRAW_DOT
  -------         PROC          _LCD_SHOW_CHAR
  D:006EH         SYMBOL        x
  D:006FH         SYMBOL        y
  D:0070H         SYMBOL        num
  D:0071H         SYMBOL        size
  D:0072H         SYMBOL        mode
  -------         DO            
  D:0073H         SYMBOL        temp
  D:0001H         SYMBOL        t1
  D:0004H         SYMBOL        t
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 18


  D:0074H         SYMBOL        y0
  D:0076H         SYMBOL        csize
  -------         ENDDO         
  C:2751H         LINE#         150
  C:2757H         LINE#         151
  C:2757H         LINE#         153
  C:275DH         LINE#         154
  C:277FH         LINE#         155
  C:2785H         LINE#         156
  C:2790H         LINE#         157
  C:2790H         LINE#         158
  C:27A5H         LINE#         159
  C:27BAH         LINE#         160
  C:27DEH         LINE#         162
  C:27E0H         LINE#         163
  C:27E0H         LINE#         164
  C:27E9H         LINE#         165
  C:27F5H         LINE#         166
  C:27FBH         LINE#         167
  C:27FDH         LINE#         168
  C:2804H         LINE#         169
  C:2819H         LINE#         170
  C:2819H         LINE#         171
  C:281CH         LINE#         172
  C:281EH         LINE#         173
  C:2826H         LINE#         175
  C:2826H         LINE#         176
  C:282AH         LINE#         177
  C:282EH         LINE#         178
  -------         ENDPROC       _LCD_SHOW_CHAR
  -------         PROC          _LCD_POW
  D:006EH         SYMBOL        m
  D:006FH         SYMBOL        n
  -------         DO            
  D:0070H         SYMBOL        result
  -------         ENDDO         
  C:3319H         LINE#         181
  C:331DH         LINE#         182
  C:331DH         LINE#         183
  C:3327H         LINE#         184
  C:3349H         LINE#         185
  C:3351H         LINE#         186
  -------         ENDPROC       _LCD_POW
  -------         PROC          _LCD_SHOW_NUM
  D:0062H         SYMBOL        x
  D:0063H         SYMBOL        y
  D:0064H         SYMBOL        num
  D:0068H         SYMBOL        len
  D:0069H         SYMBOL        size
  D:006AH         SYMBOL        mode
  -------         DO            
  D:006BH         SYMBOL        t
  D:006CH         SYMBOL        temp
  D:006DH         SYMBOL        enshow
  -------         ENDDO         
  C:282FH         LINE#         197
  C:2833H         LINE#         198
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 19


  C:2833H         LINE#         200
  C:2836H         LINE#         201
  C:2842H         LINE#         202
  C:2842H         LINE#         203
  C:286EH         LINE#         204
  C:287CH         LINE#         205
  C:287CH         LINE#         206
  C:2880H         LINE#         207
  C:2880H         LINE#         208
  C:289DH         LINE#         209
  C:28B3H         LINE#         210
  C:28B5H         LINE#         211
  C:28B8H         LINE#         213
  C:28B8H         LINE#         214
  C:28D6H         LINE#         215
  C:28DBH         LINE#         216
  -------         ENDPROC       _LCD_SHOW_NUM
  -------         PROC          _LCD_SHOW_STRING
  D:0058H         SYMBOL        x
  D:0059H         SYMBOL        y
  D:005AH         SYMBOL        width
  D:005BH         SYMBOL        height
  D:005CH         SYMBOL        size
  D:005DH         SYMBOL        p
  -------         DO            
  D:0060H         SYMBOL        x0
  -------         ENDDO         
  C:2FC7H         LINE#         222
  C:2FCDH         LINE#         223
  C:2FCDH         LINE#         224
  C:2FD0H         LINE#         225
  C:2FD6H         LINE#         226
  C:2FDCH         LINE#         227
  C:2FF1H         LINE#         228
  C:2FF1H         LINE#         229
  C:3000H         LINE#         230
  C:3007H         LINE#         231
  C:3018H         LINE#         232
  C:3020H         LINE#         233
  C:302BH         LINE#         234
  C:302DH         LINE#         235
  -------         ENDPROC       _LCD_SHOW_STRING
  -------         ENDMOD        LCD9648

  -------         MODULE        DELAY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:370EH         PUBLIC        _delay_10us
  C:34E5H         PUBLIC        _delay_ms
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          _DELAY_10US
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 20


  D:0006H         SYMBOL        ten_us
  C:370EH         LINE#         9
  C:370EH         LINE#         10
  C:370EH         LINE#         11
  C:3718H         LINE#         12
  -------         ENDPROC       _DELAY_10US
  -------         PROC          _DELAY_MS
  D:0006H         SYMBOL        ms
  -------         DO            
  D:0006H         SYMBOL        i
  D:0004H         SYMBOL        j
  -------         ENDDO         
  C:34E5H         LINE#         20
  C:34E5H         LINE#         21
  C:34E5H         LINE#         23
  C:34EEH         LINE#         24
  C:3502H         LINE#         25
  -------         ENDPROC       _DELAY_MS
  -------         ENDMOD        DELAY

  -------         MODULE        ONEWIRE
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0090H         PUBLIC        P1
  D:00B0H         PUBLIC        P3
  C:35C8H         PUBLIC        Read_DS18B20
  D:00A8H         PUBLIC        IE
  C:35B2H         PUBLIC        _Delay_OneWire
  C:3503H         PUBLIC        _Write_DS18B20
  B:0090H.4       PUBLIC        DQ
  D:00B8H         PUBLIC        IP
  C:347AH         PUBLIC        init_ds18b20
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:00C8H         PUBLIC        T2CON
  C:3388H         PUBLIC        rd_temperature
  D:00D0H         PUBLIC        PSW
  C:35AEH         SYMBOL        L?0020
  -------         PROC          L?0019
  -------         ENDPROC       L?0019
  C:35AEH         SYMBOL        L?0020
  -------         PROC          _DELAY_ONEWIRE
  D:0006H         SYMBOL        t
  -------         DO            
  D:0005H         SYMBOL        i
  -------         ENDDO         
  C:35B2H         LINE#         14
  C:35B2H         LINE#         15
  C:35B2H         LINE#         17
  C:35BCH         LINE#         18
  C:35C7H         LINE#         19
  C:35C7H         LINE#         20
  -------         ENDPROC       _DELAY_ONEWIRE
  -------         PROC          _WRITE_DS18B20
  D:0001H         SYMBOL        dat
  -------         DO            
  D:0003H         SYMBOL        i
  -------         ENDDO         
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 21


  C:3503H         LINE#         23
  C:3505H         LINE#         24
  C:3505H         LINE#         26
  C:3507H         LINE#         27
  C:3507H         LINE#         28
  C:3509H         LINE#         29
  C:350DH         LINE#         30
  C:3510H         LINE#         31
  C:3512H         LINE#         32
  C:3516H         LINE#         33
  C:351AH         LINE#         34
  -------         ENDPROC       _WRITE_DS18B20
  -------         PROC          READ_DS18B20
  -------         DO            
  D:0003H         SYMBOL        i
  D:0004H         SYMBOL        dat
  -------         ENDDO         
  C:35C8H         LINE#         38
  C:35C8H         LINE#         39
  C:35C8H         LINE#         43
  C:35CAH         LINE#         44
  C:35CAH         LINE#         45
  C:35CCH         LINE#         46
  C:35D0H         LINE#         47
  C:35D2H         LINE#         48
  C:35D5H         LINE#         49
  C:35D5H         LINE#         50
  C:35D8H         LINE#         51
  C:35D8H         LINE#         52
  C:35DBH         LINE#         53
  C:35DFH         LINE#         54
  C:35E1H         LINE#         55
  -------         ENDPROC       READ_DS18B20
  -------         PROC          INIT_DS18B20
  -------         DO            
  B:0020H.0       SYMBOL        initflag
  -------         ENDDO         
  C:347AH         LINE#         58
  C:347AH         LINE#         59
  C:347AH         LINE#         60
  C:347CH         LINE#         62
  C:347EH         LINE#         63
  C:3485H         LINE#         64
  C:3487H         LINE#         65
  C:348EH         LINE#         66
  C:3490H         LINE#         67
  C:3497H         LINE#         68
  C:349BH         LINE#         69
  C:349EH         LINE#         71
  C:34A0H         LINE#         72
  -------         ENDPROC       INIT_DS18B20
  -------         PROC          RD_TEMPERATURE
  -------         DO            
  D:0001H         SYMBOL        low
  D:0007H         SYMBOL        high
  -------         ENDDO         
  C:3388H         LINE#         75
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 22


  C:3388H         LINE#         76
  C:3388H         LINE#         78
  C:338BH         LINE#         79
  C:3390H         LINE#         80
  C:3395H         LINE#         82
  C:3398H         LINE#         83
  C:339DH         LINE#         84
  C:33A2H         LINE#         86
  C:33A7H         LINE#         87
  C:33AAH         LINE#         89
  C:33BCH         LINE#         90
  -------         ENDPROC       RD_TEMPERATURE
  -------         ENDMOD        ONEWIRE

  -------         MODULE        MOTOR
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:000EH         SYMBOL        pwm_duty
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  C:36A0H         PUBLIC        Timer1_PWM_Init
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:3700H         PUBLIC        Motor_Init
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:3733H         PUBLIC        Motor_Stop
  C:373FH         PUBLIC        Motor_Get_Speed_Voltage
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:000FH         SYMBOL        pwm_counter
  C:3095H         PUBLIC        _Motor_Calc_Speed
  B:00A8H.3       PUBLIC        ET1
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0090H.6       PUBLIC        MOTOR_FWD
  B:0088H.6       PUBLIC        TR1
  C:32DEH         PUBLIC        Timer1_PWM_ISR
  C:3578H         PUBLIC        _Motor_Control
  D:0010H         SYMBOL        motor_direction
  B:0090H.7       PUBLIC        MOTOR_REV
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          MOTOR_INIT
  C:3700H         LINE#         19
  C:3700H         LINE#         20
  C:3700H         LINE#         21
  C:3702H         LINE#         22
  C:3704H         LINE#         25
  C:3707H         LINE#         26
  C:3709H         LINE#         27
  C:370BH         LINE#         30
  -------         ENDPROC       MOTOR_INIT
  -------         PROC          TIMER1_PWM_INIT
  C:36A0H         LINE#         37
  C:36A0H         LINE#         38
  C:36A0H         LINE#         40
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 23


  C:36A3H         LINE#         41
  C:36A6H         LINE#         44
  C:36A9H         LINE#         45
  C:36ACH         LINE#         48
  C:36AEH         LINE#         49
  C:36B0H         LINE#         50
  -------         ENDPROC       TIMER1_PWM_INIT
  -------         PROC          _MOTOR_CONTROL
  D:0007H         SYMBOL        dir
  D:0006H         SYMBOL        speed
  C:3578H         LINE#         57
  C:357AH         LINE#         58
  C:357AH         LINE#         60
  C:357CH         LINE#         61
  C:357EH         LINE#         64
  C:3584H         LINE#         65
  C:3584H         LINE#         67
  C:3586H         LINE#         68
  C:3588H         LINE#         69
  C:358BH         LINE#         70
  C:358BH         LINE#         73
  C:358DH         LINE#         74
  C:358FH         LINE#         77
  -------         ENDPROC       _MOTOR_CONTROL
  -------         PROC          MOTOR_STOP
  C:3733H         LINE#         84
  C:3733H         LINE#         85
  C:3733H         LINE#         86
  -------         ENDPROC       MOTOR_STOP
  -------         PROC          MOTOR_GET_SPEED_VOLTAGE
  C:373FH         LINE#         94
  C:373FH         LINE#         95
  C:373FH         LINE#         97
  C:3742H         LINE#         98
  -------         ENDPROC       MOTOR_GET_SPEED_VOLTAGE
  -------         PROC          TIMER1_PWM_ISR
  C:32DEH         LINE#         104
  C:32E2H         LINE#         107
  C:32E5H         LINE#         108
  C:32E8H         LINE#         111
  C:32EAH         LINE#         114
  C:32F5H         LINE#         115
  C:32F5H         LINE#         116
  C:32FBH         LINE#         117
  C:32FBH         LINE#         118
  C:3304H         LINE#         119
  C:3304H         LINE#         120
  C:3304H         LINE#         121
  C:3306H         LINE#         122
  C:3308H         LINE#         123
  C:330AH         LINE#         124
  C:330AH         LINE#         125
  C:330CH         LINE#         126
  C:330EH         LINE#         127
  C:3310H         LINE#         128
  C:3310H         LINE#         129
  C:3310H         LINE#         137
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 24


  C:3310H         LINE#         138
  C:3312H         LINE#         139
  C:3314H         LINE#         140
  C:3314H         LINE#         141
  -------         ENDPROC       TIMER1_PWM_ISR
  -------         PROC          _MOTOR_CALC_SPEED
  D:005DH         SYMBOL        temp_diff
  -------         DO            
  D:0007H         SYMBOL        speed
  D:0061H         SYMBOL        abs_diff
  -------         ENDDO         
  C:3095H         LINE#         149
  C:309DH         LINE#         150
  C:309DH         LINE#         155
  C:30CEH         LINE#         158
  C:30E3H         LINE#         161
  C:30EBH         LINE#         162
  C:30F3H         LINE#         164
  C:30F3H         LINE#         165
  -------         ENDPROC       _MOTOR_CALC_SPEED
  -------         ENDMOD        MOTOR

  -------         MODULE        SENSOR
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:3747H         PUBLIC        Get_Current_Detect
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:3723H         PUBLIC        Sensor_Update_All
  C:3743H         PUBLIC        Get_NTC_Temperature
  D:00C8H         PUBLIC        T2CON
  C:374BH         PUBLIC        Sensor_Init
  D:00D0H         PUBLIC        PSW
  -------         PROC          SENSOR_INIT
  C:374BH         LINE#         13
  C:374BH         LINE#         14
  C:374BH         LINE#         16
  -------         ENDPROC       SENSOR_INIT
  -------         PROC          GET_NTC_TEMPERATURE
  C:3743H         LINE#         24
  C:3743H         LINE#         25
  C:3743H         LINE#         27
  C:3746H         LINE#         28
  -------         ENDPROC       GET_NTC_TEMPERATURE
  -------         PROC          GET_CURRENT_DETECT
  C:3747H         LINE#         35
  C:3747H         LINE#         36
  C:3747H         LINE#         38
  C:374AH         LINE#         39
  -------         ENDPROC       GET_CURRENT_DETECT
  -------         PROC          SENSOR_UPDATE_ALL
  C:3723H         LINE#         45
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 25


  C:3723H         LINE#         46
  C:3723H         LINE#         48
  C:3726H         LINE#         51
  C:3729H         LINE#         54
  -------         ENDPROC       SENSOR_UPDATE_ALL
  -------         ENDMOD        SENSOR

  -------         MODULE        DS1302
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  C:3565H         PUBLIC        _Decimal_To_BCD
  C:36F1H         PUBLIC        _BCD_To_Decimal
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  D:00A8H         PUBLIC        IE
  B:00A0H.3       PUBLIC        DS1302_DAT
  D:00B8H         PUBLIC        IP
  B:00A0H.2       PUBLIC        DS1302_CLK
  C:2C39H         PUBLIC        _DS1302_Read_Byte
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:00A0H.4       PUBLIC        DS1302_RST
  C:2D9CH         PUBLIC        _DS1302_Write_Byte
  C:2341H         PUBLIC        _DS1302_Get_Time
  C:3653H         PUBLIC        DS1302_Init
  C:302EH         PUBLIC        _DS1302_Set_Time
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          DS1302_INIT
  C:3653H         LINE#         13
  C:3653H         LINE#         14
  C:3653H         LINE#         15
  C:3655H         LINE#         16
  C:3657H         LINE#         17
  C:3659H         LINE#         20
  C:3660H         LINE#         23
  -------         ENDPROC       DS1302_INIT
  -------         PROC          _DS1302_WRITE_BYTE
  D:005AH         SYMBOL        addr
  D:005BH         SYMBOL        dat
  -------         DO            
  D:005CH         SYMBOL        i
  -------         ENDDO         
  C:2D9CH         LINE#         31
  C:2DA0H         LINE#         32
  C:2DA0H         LINE#         35
  C:2DA2H         LINE#         36
  C:2DA9H         LINE#         39
  C:2DACH         LINE#         40
  C:2DACH         LINE#         41
  C:2DAEH         LINE#         42
  C:2DBEH         LINE#         43
  C:2DC5H         LINE#         44
  C:2DC7H         LINE#         45
  C:2DCEH         LINE#         46
  C:2DD7H         LINE#         49
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 26


  C:2DDAH         LINE#         50
  C:2DDAH         LINE#         51
  C:2DDCH         LINE#         52
  C:2DECH         LINE#         53
  C:2DF3H         LINE#         54
  C:2DF5H         LINE#         55
  C:2DFCH         LINE#         56
  C:2E05H         LINE#         58
  C:2E07H         LINE#         59
  -------         ENDPROC       _DS1302_WRITE_BYTE
  -------         PROC          _DS1302_READ_BYTE
  D:005DH         SYMBOL        addr
  -------         DO            
  D:005EH         SYMBOL        i
  D:005FH         SYMBOL        dat
  -------         ENDDO         
  C:2C39H         LINE#         68
  C:2C3BH         LINE#         69
  C:2C3BH         LINE#         70
  C:2C3EH         LINE#         72
  C:2C40H         LINE#         73
  C:2C46H         LINE#         76
  C:2C49H         LINE#         77
  C:2C4CH         LINE#         78
  C:2C4CH         LINE#         79
  C:2C4EH         LINE#         80
  C:2C5EH         LINE#         81
  C:2C65H         LINE#         82
  C:2C67H         LINE#         83
  C:2C6EH         LINE#         84
  C:2C77H         LINE#         87
  C:2C7AH         LINE#         88
  C:2C7AH         LINE#         89
  C:2C7CH         LINE#         90
  C:2C83H         LINE#         91
  C:2C85H         LINE#         92
  C:2C97H         LINE#         93
  C:2C9EH         LINE#         94
  C:2CA7H         LINE#         96
  C:2CA9H         LINE#         97
  C:2CB0H         LINE#         99
  C:2CB2H         LINE#         100
  -------         ENDPROC       _DS1302_READ_BYTE
  -------         PROC          _DS1302_SET_TIME
  D:0057H         SYMBOL        time
  C:302EH         LINE#         107
  C:3034H         LINE#         108
  C:3034H         LINE#         109
  C:3041H         LINE#         110
  C:304EH         LINE#         111
  C:305BH         LINE#         112
  C:306EH         LINE#         113
  C:3081H         LINE#         114
  -------         ENDPROC       _DS1302_SET_TIME
  -------         PROC          _DS1302_GET_TIME
  D:0057H         SYMBOL        time
  -------         DO            
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 27


  D:005AH         SYMBOL        raw_sec
  D:005BH         SYMBOL        raw_min
  D:005CH         SYMBOL        raw_hour
  -------         ENDDO         
  C:2341H         LINE#         122
  C:2347H         LINE#         123
  C:2347H         LINE#         127
  C:234EH         LINE#         128
  C:2355H         LINE#         129
  C:235CH         LINE#         132
  C:2376H         LINE#         133
  C:2376H         LINE#         135
  C:2383H         LINE#         136
  C:238AH         LINE#         137
  C:2392H         LINE#         138
  C:239AH         LINE#         139
  C:23A2H         LINE#         140
  C:23A4H         LINE#         141
  C:23A6H         LINE#         143
  C:23A6H         LINE#         145
  C:23B6H         LINE#         146
  C:23CBH         LINE#         147
  C:23E0H         LINE#         148
  C:23F9H         LINE#         149
  C:2412H         LINE#         150
  C:2424H         LINE#         151
  C:2424H         LINE#         154
  C:242FH         LINE#         155
  C:2437H         LINE#         156
  C:243FH         LINE#         157
  C:2447H         LINE#         158
  C:244FH         LINE#         159
  C:2457H         LINE#         160
  -------         ENDPROC       _DS1302_GET_TIME
  -------         PROC          _BCD_TO_DECIMAL
  D:0007H         SYMBOL        bcd
  C:36F1H         LINE#         166
  C:36F1H         LINE#         167
  C:36F1H         LINE#         168
  C:36FFH         LINE#         169
  -------         ENDPROC       _BCD_TO_DECIMAL
  C:3561H         SYMBOL        L?0034
  -------         PROC          L?0033
  -------         ENDPROC       L?0033
  C:3561H         SYMBOL        L?0034
  -------         PROC          _DECIMAL_TO_BCD
  D:0007H         SYMBOL        dec
  C:3565H         LINE#         175
  C:3565H         LINE#         176
  C:3565H         LINE#         177
  C:3577H         LINE#         178
  -------         ENDPROC       _DECIMAL_TO_BCD
  -------         ENDMOD        DS1302

  -------         MODULE        ALARM
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 28


  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  B:0080H.0       PUBLIC        LED1
  B:0080H.1       PUBLIC        LED2
  D:0008H         SYMBOL        buzzer_counter
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:000AH         SYMBOL        led_counter
  C:34C6H         PUBLIC        _LED_Control
  C:2AA9H         PUBLIC        _Calc_Alarm_Level
  D:000CH         SYMBOL        buzzer_state
  C:2E7FH         PUBLIC        Update_Alarm_Output
  C:2BB6H         PUBLIC        Alarm_Process
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  D:000DH         SYMBOL        led_state
  C:3196H         PUBLIC        _Buzzer_Control
  B:0090H.5       PUBLIC        BUZZER
  C:3667H         PUBLIC        Alarm_Init
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          ALARM_INIT
  C:3667H         LINE#         18
  C:3667H         LINE#         19
  C:3667H         LINE#         20
  C:3669H         LINE#         21
  C:366BH         LINE#         22
  C:366DH         LINE#         24
  C:3672H         LINE#         25
  C:3676H         LINE#         26
  C:3678H         LINE#         27
  C:367AH         LINE#         28
  -------         ENDPROC       ALARM_INIT
  -------         PROC          _BUZZER_CONTROL
  D:0007H         SYMBOL        enable
  D:0004H         SYMBOL        freq
  -------         DO            
  D:005DH         SYMBOL        period
  -------         ENDDO         
  C:3196H         LINE#         35
  C:3196H         LINE#         36
  C:3196H         LINE#         37
  C:31A2H         LINE#         38
  C:31A2H         LINE#         40
  C:31B4H         LINE#         42
  C:31BCH         LINE#         43
  C:31C5H         LINE#         44
  C:31C5H         LINE#         45
  C:31CAH         LINE#         46
  C:31D6H         LINE#         47
  C:31DCH         LINE#         48
  C:31DCH         LINE#         49
  C:31DDH         LINE#         51
  C:31DDH         LINE#         52
  C:31DFH         LINE#         53
  C:31E2H         LINE#         54
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 29


  C:31E6H         LINE#         55
  C:31E6H         LINE#         56
  -------         ENDPROC       _BUZZER_CONTROL
  -------         PROC          _LED_CONTROL
  D:0007H         SYMBOL        led_num
  D:0005H         SYMBOL        state
  C:34C6H         LINE#         63
  C:34C6H         LINE#         64
  C:34C6H         LINE#         65
  C:34CDH         LINE#         66
  C:34CDH         LINE#         67
  C:34CDH         LINE#         68
  C:34D3H         LINE#         69
  C:34D8H         LINE#         70
  C:34D9H         LINE#         72
  C:34D9H         LINE#         73
  C:34DFH         LINE#         74
  C:34E4H         LINE#         75
  C:34E4H         LINE#         77
  C:34E4H         LINE#         78
  C:34E4H         LINE#         79
  C:34E4H         LINE#         80
  -------         ENDPROC       _LED_CONTROL
  -------         PROC          _CALC_ALARM_LEVEL
  D:005DH         SYMBOL        deviation
  -------         DO            
  D:0061H         SYMBOL        level
  -------         ENDDO         
  C:2AA9H         LINE#         88
  C:2AB1H         LINE#         89
  C:2AB1H         LINE#         93
  C:2AC5H         LINE#         94
  C:2ACAH         LINE#         95
  C:2ADEH         LINE#         96
  C:2AE3H         LINE#         97
  C:2AF7H         LINE#         98
  C:2AFCH         LINE#         99
  C:2B0FH         LINE#         100
  C:2B14H         LINE#         101
  C:2B28H         LINE#         102
  C:2B2DH         LINE#         104
  C:2B30H         LINE#         106
  C:2B32H         LINE#         107
  -------         ENDPROC       _CALC_ALARM_LEVEL
  -------         PROC          ALARM_PROCESS
  -------         DO            
  D:0057H         SYMBOL        temp_deviation
  D:0003H         SYMBOL        alarm_level
  D:005BH         SYMBOL        buzzer_freq
  -------         ENDDO         
  C:2BB6H         LINE#         113
  C:2BB6H         LINE#         114
  C:2BB6H         LINE#         120
  C:2BD1H         LINE#         121
  C:2BF6H         LINE#         124
  C:2C03H         LINE#         125
  C:2C05H         LINE#         128
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 30


  C:2C08H         LINE#         129
  C:2C08H         LINE#         130
  C:2C0CH         LINE#         131
  C:2C0EH         LINE#         133
  C:2C0EH         LINE#         136
  C:2C23H         LINE#         137
  C:2C23H         LINE#         140
  C:2C36H         LINE#         143
  -------         ENDPROC       ALARM_PROCESS
  -------         PROC          UPDATE_ALARM_OUTPUT
  -------         DO            
  D:0006H         SYMBOL        led_period
  -------         ENDDO         
  C:2E7FH         LINE#         150
  C:2E7FH         LINE#         151
  C:2E7FH         LINE#         155
  C:2E83H         LINE#         156
  C:2E83H         LINE#         157
  C:2E85H         LINE#         158
  C:2E87H         LINE#         159
  C:2E8BH         LINE#         160
  C:2E8DH         LINE#         161
  C:2E8EH         LINE#         163
  C:2E8EH         LINE#         166
  C:2E9AH         LINE#         167
  C:2EA7H         LINE#         169
  C:2EAFH         LINE#         170
  C:2EB6H         LINE#         171
  C:2EB6H         LINE#         172
  C:2EBBH         LINE#         173
  C:2EC7H         LINE#         176
  C:2ECEH         LINE#         177
  C:2ECEH         LINE#         178
  C:2ED4H         LINE#         179
  C:2ED6H         LINE#         180
  C:2ED7H         LINE#         181
  C:2EE0H         LINE#         182
  C:2EE0H         LINE#         183
  C:2EE4H         LINE#         184
  C:2EE7H         LINE#         185
  C:2EE8H         LINE#         187
  C:2EE8H         LINE#         188
  C:2EECH         LINE#         189
  C:2EEEH         LINE#         190
  C:2EEEH         LINE#         191
  C:2EEEH         LINE#         192
  C:2EEEH         LINE#         193
  -------         ENDPROC       UPDATE_ALARM_OUTPUT
  -------         ENDMOD        ALARM

  -------         MODULE        DISPLAY
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:1E07H         PUBLIC        Display_Main_Screen
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 31


  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  C:36D2H         PUBLIC        Display_Update
  C:1F72H         PUBLIC        _Display_Float
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  C:2458H         PUBLIC        _Display_Time
  C:3739H         PUBLIC        Display_Init
  C:2201H         PUBLIC        Display_Menu
  D:00C8H         PUBLIC        T2CON
  D:00D0H         PUBLIC        PSW
  -------         PROC          DISPLAY_INIT
  C:3739H         LINE#         12
  C:3739H         LINE#         13
  C:3739H         LINE#         14
  C:373CH         LINE#         15
  -------         ENDPROC       DISPLAY_INIT
  -------         PROC          _DISPLAY_FLOAT
  D:0007H         SYMBOL        x
  D:0059H         SYMBOL        y
  D:005AH         SYMBOL        value
  D:005EH         SYMBOL        precision
  -------         DO            
  D:005FH         SYMBOL        integer_part
  D:0006H         SYMBOL        decimal_part
  D:0061H         SYMBOL        pos
  -------         ENDDO         
  C:1F72H         LINE#         23
  C:1F74H         LINE#         24
  C:1F74H         LINE#         27
  C:1F76H         LINE#         30
  C:1F88H         LINE#         31
  C:1F88H         LINE#         32
  C:1F97H         LINE#         33
  C:1F9DH         LINE#         34
  C:1FB0H         LINE#         35
  C:1FB0H         LINE#         38
  C:1FD3H         LINE#         41
  C:1FE2H         LINE#         44
  C:1FEDH         LINE#         45
  C:1FEDH         LINE#         46
  C:2005H         LINE#         47
  C:2007H         LINE#         48
  C:2009H         LINE#         49
  C:2015H         LINE#         50
  C:2015H         LINE#         51
  C:202EH         LINE#         52
  C:2030H         LINE#         53
  C:2032H         LINE#         55
  C:2032H         LINE#         56
  C:204BH         LINE#         57
  C:2051H         LINE#         58
  C:2051H         LINE#         61
  C:205EH         LINE#         62
  C:205EH         LINE#         63
  C:206DH         LINE#         64
  C:2073H         LINE#         67
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 32


  C:209BH         LINE#         68
  C:20A8H         LINE#         70
  C:20B9H         LINE#         71
  C:20B9H         LINE#         72
  -------         ENDPROC       _DISPLAY_FLOAT
  -------         PROC          _DISPLAY_TIME
  D:0007H         SYMBOL        x
  D:0058H         SYMBOL        y
  -------         DO            
  D:0059H         SYMBOL        pos
  -------         ENDDO         
  C:2458H         LINE#         79
  C:245AH         LINE#         80
  C:245AH         LINE#         81
  C:245CH         LINE#         84
  C:2463H         LINE#         85
  C:2463H         LINE#         86
  C:246EH         LINE#         87
  C:2474H         LINE#         88
  C:2474H         LINE#         89
  C:249AH         LINE#         90
  C:24ACH         LINE#         92
  C:24BBH         LINE#         93
  C:24C1H         LINE#         95
  C:24C8H         LINE#         96
  C:24C8H         LINE#         97
  C:24D7H         LINE#         98
  C:24DDH         LINE#         99
  C:24DDH         LINE#         100
  C:2503H         LINE#         101
  C:2515H         LINE#         103
  C:2524H         LINE#         104
  C:252AH         LINE#         106
  C:2531H         LINE#         107
  C:2531H         LINE#         108
  C:2540H         LINE#         109
  C:2546H         LINE#         110
  C:2546H         LINE#         111
  -------         ENDPROC       _DISPLAY_TIME
  -------         PROC          DISPLAY_MAIN_SCREEN
  C:1E07H         LINE#         118
  C:1E07H         LINE#         119
  C:1E07H         LINE#         121
  C:1E0AH         LINE#         124
  C:1E21H         LINE#         125
  C:1E37H         LINE#         126
  C:1E4FH         LINE#         127
  C:1E65H         LINE#         128
  C:1E7DH         LINE#         131
  C:1E95H         LINE#         132
  C:1EABH         LINE#         133
  C:1EC3H         LINE#         134
  C:1EDCH         LINE#         137
  C:1EF4H         LINE#         138
  C:1EFBH         LINE#         141
  C:1F13H         LINE#         142
  C:1F29H         LINE#         143
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 33


  C:1F41H         LINE#         144
  C:1F57H         LINE#         145
  C:1F6FH         LINE#         148
  -------         ENDPROC       DISPLAY_MAIN_SCREEN
  -------         PROC          DISPLAY_MENU
  C:2201H         LINE#         155
  C:2201H         LINE#         156
  C:2201H         LINE#         157
  C:2204H         LINE#         159
  C:2216H         LINE#         160
  C:2216H         LINE#         161
  C:2216H         LINE#         162
  C:222DH         LINE#         163
  C:2245H         LINE#         164
  C:225BH         LINE#         165
  C:2273H         LINE#         166
  C:2273H         LINE#         167
  C:2275H         LINE#         169
  C:2275H         LINE#         170
  C:228CH         LINE#         171
  C:22A4H         LINE#         172
  C:22ABH         LINE#         173
  C:22C3H         LINE#         174
  C:22C5H         LINE#         176
  C:22C5H         LINE#         177
  C:22DCH         LINE#         178
  C:22F4H         LINE#         179
  C:230DH         LINE#         180
  C:2325H         LINE#         181
  C:233EH         LINE#         182
  C:233EH         LINE#         183
  C:233EH         LINE#         185
  -------         ENDPROC       DISPLAY_MENU
  -------         PROC          DISPLAY_UPDATE
  C:36D2H         LINE#         192
  C:36D2H         LINE#         193
  C:36D2H         LINE#         194
  C:36D6H         LINE#         195
  C:36D6H         LINE#         196
  C:36D9H         LINE#         197
  C:36D9H         LINE#         198
  C:36DEH         LINE#         199
  C:36DEH         LINE#         200
  C:36E1H         LINE#         201
  C:36E1H         LINE#         202
  -------         ENDPROC       DISPLAY_UPDATE
  -------         ENDMOD        DISPLAY

  -------         MODULE        UART
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  C:20C1H         PUBLIC        _UART_Send_Float
  C:367BH         PUBLIC        _UART_Send_String
  D:00A8H         PUBLIC        IE
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 34


  D:00B8H         PUBLIC        IP
  B:0098H.1       PUBLIC        TI
  C:0003H         PUBLIC        _UART_Send_Byte
  D:0099H         PUBLIC        SBUF
  C:36B1H         PUBLIC        UART_Init
  D:0098H         PUBLIC        SCON
  D:0089H         PUBLIC        TMOD
  D:0088H         PUBLIC        TCON
  D:008DH         PUBLIC        TH1
  D:008BH         PUBLIC        TL1
  B:0088H.6       PUBLIC        TR1
  D:00C8H         PUBLIC        T2CON
  C:2CB3H         PUBLIC        UART_Send_Alarm_Info
  C:2665H         PUBLIC        UART_Send_System_Data
  D:00D0H         PUBLIC        PSW
  -------         PROC          UART_INIT
  C:36B1H         LINE#         12
  C:36B1H         LINE#         13
  C:36B1H         LINE#         14
  C:36B4H         LINE#         15
  C:36B7H         LINE#         16
  C:36BAH         LINE#         17
  C:36BDH         LINE#         18
  C:36BFH         LINE#         19
  C:36C1H         LINE#         20
  -------         ENDPROC       UART_INIT
  -------         PROC          _UART_SEND_BYTE
  D:0007H         SYMBOL        dat
  C:0003H         LINE#         27
  C:0003H         LINE#         28
  C:0003H         LINE#         29
  C:0005H         LINE#         30
  C:0008H         LINE#         31
  C:000AH         LINE#         32
  -------         ENDPROC       _UART_SEND_BYTE
  -------         PROC          _UART_SEND_STRING
  D:0001H         SYMBOL        str
  C:367BH         LINE#         39
  C:367BH         LINE#         40
  C:367BH         LINE#         41
  C:3681H         LINE#         42
  C:3681H         LINE#         43
  C:3684H         LINE#         44
  C:368BH         LINE#         45
  C:368DH         LINE#         46
  -------         ENDPROC       _UART_SEND_STRING
  C:20BAH         SYMBOL        L?0029
  -------         PROC          L?0028
  -------         ENDPROC       L?0028
  C:20BAH         SYMBOL        L?0029
  -------         PROC          _UART_SEND_FLOAT
  D:005BH         SYMBOL        value
  D:005FH         SYMBOL        precision
  -------         DO            
  D:0002H         SYMBOL        integer_part
  D:0060H         SYMBOL        decimal_part
  D:0064H         SYMBOL        i
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 35


  D:0065H         SYMBOL        digit
  -------         DO            
  D:0066H         SYMBOL        digits
  D:0070H         SYMBOL        digit_count
  -------         ENDDO         
  -------         ENDDO         
  C:20C1H         LINE#         53
  C:20C9H         LINE#         54
  C:20C9H         LINE#         61
  C:20DBH         LINE#         62
  C:20DBH         LINE#         63
  C:20E0H         LINE#         64
  C:20F3H         LINE#         65
  C:20F3H         LINE#         68
  C:2102H         LINE#         69
  C:2106H         LINE#         70
  C:2106H         LINE#         71
  C:210BH         LINE#         72
  C:210DH         LINE#         74
  C:210DH         LINE#         77
  C:2110H         LINE#         79
  C:211BH         LINE#         80
  C:211BH         LINE#         81
  C:2132H         LINE#         82
  C:2141H         LINE#         83
  C:2143H         LINE#         85
  C:214DH         LINE#         86
  C:214DH         LINE#         87
  C:2159H         LINE#         88
  C:215DH         LINE#         89
  C:215DH         LINE#         92
  C:2167H         LINE#         93
  C:2167H         LINE#         94
  C:216CH         LINE#         97
  C:219AH         LINE#         98
  C:21A4H         LINE#         99
  C:21A4H         LINE#         100
  C:21BEH         LINE#         101
  C:21C3H         LINE#         102
  C:21CBH         LINE#         103
  C:21FCH         LINE#         104
  C:2200H         LINE#         105
  C:2200H         LINE#         106
  -------         ENDPROC       _UART_SEND_FLOAT
  -------         PROC          UART_SEND_SYSTEM_DATA
  C:2665H         LINE#         112
  C:2665H         LINE#         113
  C:2665H         LINE#         116
  C:266EH         LINE#         117
  C:267CH         LINE#         119
  C:2685H         LINE#         120
  C:2693H         LINE#         121
  C:269CH         LINE#         123
  C:26A3H         LINE#         124
  C:26B1H         LINE#         126
  C:26BAH         LINE#         127
  C:26C8H         LINE#         128
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 36


  C:26D1H         LINE#         130
  C:26D8H         LINE#         131
  C:26E4H         LINE#         132
  C:26E9H         LINE#         133
  C:26EEH         LINE#         134
  C:26FAH         LINE#         135
  C:26FFH         LINE#         136
  C:2704H         LINE#         137
  C:2710H         LINE#         138
  C:2715H         LINE#         140
  C:271EH         LINE#         141
  C:272CH         LINE#         143
  C:2735H         LINE#         144
  C:273AH         LINE#         146
  C:2743H         LINE#         147
  C:2748H         LINE#         149
  -------         ENDPROC       UART_SEND_SYSTEM_DATA
  -------         PROC          UART_SEND_ALARM_INFO
  -------         DO            
  D:0057H         SYMBOL        deviation
  -------         ENDDO         
  C:2CB3H         LINE#         156
  C:2CB3H         LINE#         157
  C:2CB3H         LINE#         160
  C:2CBAH         LINE#         161
  C:2CBAH         LINE#         162
  C:2CC3H         LINE#         163
  C:2CC8H         LINE#         165
  C:2CD1H         LINE#         166
  C:2CECH         LINE#         167
  C:2D11H         LINE#         168
  C:2D1FH         LINE#         170
  C:2D28H         LINE#         171
  C:2D28H         LINE#         172
  -------         ENDPROC       UART_SEND_ALARM_INFO
  -------         ENDMOD        UART

  -------         MODULE        ADC
  C:0000H         SYMBOL        _ICE_DUMMY_
  D:0080H         PUBLIC        P0
  D:0090H         PUBLIC        P1
  D:00A0H         PUBLIC        P2
  D:00B0H         PUBLIC        P3
  B:00B0H.5       PUBLIC        ADC_CLK
  C:28DCH         PUBLIC        ADC_Get_NTC_Temperature
  D:00A8H         PUBLIC        IE
  D:00B8H         PUBLIC        IP
  D:0098H         PUBLIC        SCON
  D:0088H         PUBLIC        TCON
  B:00B0H.3       PUBLIC        ADC_DI
  B:00B0H.2       PUBLIC        ADC_DO
  C:372CH         PUBLIC        ADC_Init
  B:00B0H.4       PUBLIC        ADC_CS
  C:344EH         PUBLIC        _ADC_Read_Channel
  C:297EH         PUBLIC        ADC_Get_Current_Detect
  D:00C8H         PUBLIC        T2CON
  C:33F1H         PUBLIC        ADC_SPI_Read_Byte
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 37


  C:3267H         PUBLIC        ADC_Get_Motor_Voltage
  D:00D0H         PUBLIC        PSW
  C:33BDH         PUBLIC        _ADC_SPI_Write_Byte
  -------         PROC          ADC_INIT
  C:372CH         LINE#         13
  C:372CH         LINE#         14
  C:372CH         LINE#         15
  C:372EH         LINE#         16
  C:3730H         LINE#         17
  C:3732H         LINE#         18
  -------         ENDPROC       ADC_INIT
  -------         PROC          _ADC_SPI_WRITE_BYTE
  D:0061H         SYMBOL        dat
  -------         DO            
  D:0062H         SYMBOL        i
  -------         ENDDO         
  C:33BDH         LINE#         25
  C:33BFH         LINE#         26
  C:33BFH         LINE#         29
  C:33C2H         LINE#         30
  C:33C2H         LINE#         31
  C:33C4H         LINE#         32
  C:33C9H         LINE#         33
  C:33CDH         LINE#         35
  C:33CFH         LINE#         36
  C:33D6H         LINE#         37
  C:33D8H         LINE#         38
  C:33DFH         LINE#         39
  C:33E5H         LINE#         40
  C:33EEH         LINE#         41
  C:33F0H         LINE#         42
  -------         ENDPROC       _ADC_SPI_WRITE_BYTE
  -------         PROC          ADC_SPI_READ_BYTE
  -------         DO            
  D:0061H         SYMBOL        i
  D:0062H         SYMBOL        dat
  -------         ENDDO         
  C:33F1H         LINE#         49
  C:33F1H         LINE#         50
  C:33F1H         LINE#         51
  C:33F4H         LINE#         53
  C:33F6H         LINE#         54
  C:33F6H         LINE#         55
  C:33F8H         LINE#         56
  C:33FFH         LINE#         57
  C:3401H         LINE#         58
  C:3407H         LINE#         59
  C:340AH         LINE#         60
  C:340DH         LINE#         61
  C:3414H         LINE#         62
  C:341DH         LINE#         63
  C:341FH         LINE#         64
  C:3421H         LINE#         65
  -------         ENDPROC       ADC_SPI_READ_BYTE
  -------         PROC          _ADC_READ_CHANNEL
  D:0007H         SYMBOL        ch
  -------         DO            
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 38


  D:005FH         SYMBOL        cmd
  D:0060H         SYMBOL        high_byte
  D:0005H         SYMBOL        low_byte
  D:0006H         SYMBOL        adc_value
  -------         ENDDO         
  C:344EH         LINE#         73
  C:344EH         LINE#         74
  C:344EH         LINE#         80
  C:3458H         LINE#         83
  C:345AH         LINE#         84
  C:3461H         LINE#         87
  C:3466H         LINE#         90
  C:346BH         LINE#         93
  C:3470H         LINE#         96
  C:3472H         LINE#         99
  C:3479H         LINE#         101
  C:3479H         LINE#         102
  -------         ENDPROC       _ADC_READ_CHANNEL
  -------         PROC          ADC_GET_MOTOR_VOLTAGE
  -------         DO            
  D:0006H         SYMBOL        adc_value
  D:0057H         SYMBOL        voltage
  -------         ENDDO         
  C:3267H         LINE#         109
  C:3267H         LINE#         110
  C:3267H         LINE#         114
  C:326CH         LINE#         117
  C:329AH         LINE#         120
  C:32A2H         LINE#         122
  C:32A2H         LINE#         123
  -------         ENDPROC       ADC_GET_MOTOR_VOLTAGE
  -------         PROC          ADC_GET_NTC_TEMPERATURE
  -------         DO            
  D:0006H         SYMBOL        adc_value
  D:0057H         SYMBOL        voltage
  D:005BH         SYMBOL        temperature
  -------         ENDDO         
  C:28DCH         LINE#         130
  C:28DCH         LINE#         131
  C:28DCH         LINE#         135
  C:28E1H         LINE#         138
  C:2905H         LINE#         141
  C:2929H         LINE#         144
  C:2948H         LINE#         145
  C:2969H         LINE#         148
  C:2975H         LINE#         150
  C:297DH         LINE#         151
  -------         ENDPROC       ADC_GET_NTC_TEMPERATURE
  -------         PROC          ADC_GET_CURRENT_DETECT
  -------         DO            
  D:0006H         SYMBOL        adc_value
  D:0057H         SYMBOL        voltage
  D:005BH         SYMBOL        current
  -------         ENDDO         
  C:297EH         LINE#         158
  C:297EH         LINE#         159
  C:297EH         LINE#         163
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 39


  C:2983H         LINE#         166
  C:29A7H         LINE#         169
  C:29CCH         LINE#         172
  C:29E7H         LINE#         173
  C:2A08H         LINE#         176
  C:2A14H         LINE#         178
  C:2A1CH         LINE#         179
  -------         ENDPROC       ADC_GET_CURRENT_DETECT
  -------         ENDMOD        ADC

  -------         MODULE        ?C?FPADD
  C:17E5H         PUBLIC        ?C?FPADD
  C:17E1H         PUBLIC        ?C?FPSUB
  -------         ENDMOD        ?C?FPADD

  -------         MODULE        ?C?FPMUL
  C:18D6H         PUBLIC        ?C?FPMUL
  -------         ENDMOD        ?C?FPMUL

  -------         MODULE        ?C?FPDIV
  C:19DFH         PUBLIC        ?C?FPDIV
  -------         ENDMOD        ?C?FPDIV

  -------         MODULE        ?C?FPCMP
  C:1A7EH         PUBLIC        ?C?FPCMP
  C:1A7CH         PUBLIC        ?C?FPCMP3
  -------         ENDMOD        ?C?FPCMP

  -------         MODULE        ?C?FPNEG
  C:1AF5H         PUBLIC        ?C?FPNEG
  -------         ENDMOD        ?C?FPNEG

  -------         MODULE        ?C?FCAST
  C:1B10H         PUBLIC        ?C?FCASTC
  C:1B0BH         PUBLIC        ?C?FCASTI
  C:1B06H         PUBLIC        ?C?FCASTL
  -------         ENDMOD        ?C?FCAST

  -------         MODULE        ?C?CASTF
  C:1B44H         PUBLIC        ?C?CASTF
  -------         ENDMOD        ?C?CASTF

  -------         MODULE        ?C?CLDPTR
  C:1BC5H         PUBLIC        ?C?CLDPTR
  -------         ENDMOD        ?C?CLDPTR

  -------         MODULE        ?C?CLDOPTR
  C:1BDEH         PUBLIC        ?C?CLDOPTR
  -------         ENDMOD        ?C?CLDOPTR

  -------         MODULE        ?C?CSTPTR
  C:1C0BH         PUBLIC        ?C?CSTPTR
  -------         ENDMOD        ?C?CSTPTR

  -------         MODULE        ?C?CSTOPTR
  C:1C1DH         PUBLIC        ?C?CSTOPTR
  -------         ENDMOD        ?C?CSTOPTR
BL51 BANKED LINKER/LOCATER V6.22                                                      07/07/2025  09:58:07  PAGE 40



  -------         MODULE        ?C?IMUL
  C:1C3FH         PUBLIC        ?C?IMUL
  -------         ENDMOD        ?C?IMUL

  -------         MODULE        ?C?UIDIV
  C:1C51H         PUBLIC        ?C?UIDIV
  -------         ENDMOD        ?C?UIDIV

  -------         MODULE        ?C?SIDIV
  C:1CA6H         PUBLIC        ?C?SIDIV
  -------         ENDMOD        ?C?SIDIV

  -------         MODULE        ?C?LMUL
  C:1CDCH         PUBLIC        ?C?LMUL
  -------         ENDMOD        ?C?LMUL

  -------         MODULE        ?C?ULDIV
  C:1D67H         PUBLIC        ?C?ULDIV
  -------         ENDMOD        ?C?ULDIV

  -------         MODULE        ?C?LNEG
  C:1DF9H         PUBLIC        ?C?LNEG
  -------         ENDMOD        ?C?LNEG

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_AD_READ?IIC

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?MOTOR_STOP?MOTOR

*** WARNING L16: UNCALLED SEGMENT, IGNORED FOR OVERLAY PROCESS
    SEGMENT: ?PR?_LED_CONTROL?ALARM

Program Size: data=104.1 xdata=576 code=14157
LINK/LOCATE RUN COMPLETE.  3 WARNING(S),  0 ERROR(S)
