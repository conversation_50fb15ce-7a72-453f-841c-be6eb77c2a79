#include "UART.h"

/*=============================================================================
 * 串口通信模块实现
 * 功能：串口初始化、数据发送、系统状态输出
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数名称: UART_Init
 * 功能描述: 串口初始化，波特率9600
 *----------------------------------------------------------------------------*/
void UART_Init(void)
{
    SCON = 0x50;    // 串口模式1，允许接收
    TMOD |= 0x20;   // 定时器1模式2，8位自动重装
    TH1 = 0xFD;     // 波特率9600@11.0592MHz
    TL1 = 0xFD;
    TR1 = 1;        // 启动定时器1
    TI = 1;         // 发送中断标志置位
}

/*-----------------------------------------------------------------------------
 * 函数名称: UART_Send_Byte
 * 功能描述: 发送单个字节
 * 输入参数: dat - 要发送的字节
 *----------------------------------------------------------------------------*/
void UART_Send_Byte(unsigned char dat)
{
    SBUF = dat;     // 发送数据
    while(!TI);     // 等待发送完成
    TI = 0;         // 清除发送标志
}

/*-----------------------------------------------------------------------------
 * 函数名称: UART_Send_String
 * 功能描述: 发送字符串
 * 输入参数: str - 字符串指针
 *----------------------------------------------------------------------------*/
void UART_Send_String(unsigned char *str)
{
    while(*str)
    {
        UART_Send_Byte(*str);
        str++;
    }
}

/*-----------------------------------------------------------------------------
 * 函数名称: UART_Send_Float
 * 功能描述: 发送浮点数
 * 输入参数: value - 浮点数值, precision - 小数位数
 *----------------------------------------------------------------------------*/
void UART_Send_Float(float value, unsigned char precision)
{
    int integer_part;
    float decimal_part;
    unsigned char i;
    unsigned char digit;
    
    // 处理负数
    if(value < 0)
    {
        UART_Send_Byte('-');
        value = -value;
    }
    
    // 整数部分
    integer_part = (int)value;
    if(integer_part == 0)
    {
        UART_Send_Byte('0');
    }
    else
    {
        // 递归发送整数部分
        unsigned char digits[10];
        unsigned char digit_count = 0;
        
        while(integer_part > 0)
        {
            digits[digit_count++] = integer_part % 10;
            integer_part /= 10;
        }
        
        for(i = digit_count; i > 0; i--)
        {
            UART_Send_Byte('0' + digits[i-1]);
        }
    }
    
    // 小数点
    if(precision > 0)
    {
        UART_Send_Byte('.');
        
        // 小数部分
        decimal_part = value - (int)value;
        for(i = 0; i < precision; i++)
        {
            decimal_part *= 10;
            digit = (unsigned char)decimal_part;
            UART_Send_Byte('0' + digit);
            decimal_part -= digit;
        }
    }
}

/*-----------------------------------------------------------------------------
 * 函数名称: UART_Send_System_Data
 * 功能描述: 发送系统数据
 *----------------------------------------------------------------------------*/
void UART_Send_System_Data(void)
{
    // 发送数据格式：TEMP:25.6,MOTOR:1.2V,NTC:45.2,CURRENT:0.8A,TIME:12:30:45
    
    UART_Send_String("TEMP:");
    UART_Send_Float(g_system.current_temp, 1);
    
    UART_Send_String(",MOTOR:");
    UART_Send_Float(g_system.motor_voltage, 1);
    UART_Send_String("V");
    
    UART_Send_String(",NTC:");
    UART_Send_Float(g_system.chip_temp, 1);
    
    UART_Send_String(",CURRENT:");
    UART_Send_Float(g_system.current_detect, 2);
    UART_Send_String("A");
    
    UART_Send_String(",TIME:");
    if(g_system.hour < 10) UART_Send_Byte('0');
    UART_Send_Float(g_system.hour, 0);
    UART_Send_Byte(':');
    if(g_system.minute < 10) UART_Send_Byte('0');
    UART_Send_Float(g_system.minute, 0);
    UART_Send_Byte(':');
    if(g_system.second < 10) UART_Send_Byte('0');
    UART_Send_Float(g_system.second, 0);
    
    UART_Send_String(",TARGET:");
    UART_Send_Float(g_system.target_temp, 1);
    
    UART_Send_String(",MOTOR_STATE:");
    UART_Send_Float(g_system.motor_state, 0);
    
    UART_Send_String(",MOTOR_SPEED:");
    UART_Send_Float(g_system.motor_speed, 0);
    
    UART_Send_String("\r\n");  // 换行
}

/*-----------------------------------------------------------------------------
 * 函数名称: UART_Send_Alarm_Info
 * 功能描述: 发送报警信息
 *----------------------------------------------------------------------------*/
void UART_Send_Alarm_Info(void)
{
    float deviation;

    if(g_system.alarm_level > 0)
    {
        UART_Send_String("ALARM:LEVEL");
        UART_Send_Float(g_system.alarm_level, 0);

        UART_Send_String(",DEVIATION:");
        deviation = g_system.current_temp - g_system.target_temp;
        if(deviation < 0) deviation = -deviation;
        UART_Send_Float(deviation, 1);

        UART_Send_String("\r\n");
    }
}
