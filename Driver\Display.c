#include "Display.h"

/*=============================================================================
 * 显示功能扩展模块实现
 * 功能：LCD多参数显示、菜单界面、数据格式化
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 函数名称: Display_Init
 * 功能描述: 显示模块初始化
 *----------------------------------------------------------------------------*/
void Display_Init(void)
{
    lcd9648_init();   // 初始化LCD
    lcd9648_clear();  // 清屏
}

/*-----------------------------------------------------------------------------
 * 函数名称: Display_Float
 * 功能描述: 在指定位置显示浮点数
 * 输入参数: x,y - 坐标, value - 数值, precision - 小数位数
 *----------------------------------------------------------------------------*/
void Display_Float(unsigned char x, unsigned char y, float value, unsigned char precision)
{
    unsigned int integer_part;
    unsigned int decimal_part;
    unsigned char pos = x;

    // 处理负数
    if(value < 0)
    {
        lcd_show_char(pos, y, '-', 12, 0);
        pos += 6;
        value = -value;
    }

    // 限制数值范围，避免溢出
    if(value > 999.9) value = 999.9;

    // 整数部分
    integer_part = (unsigned int)value;

    // 显示整数部分（最多3位）
    if(integer_part >= 100)
    {
        lcd_show_num(pos, y, integer_part, 3, 12, 0);
        pos += 18;
    }
    else if(integer_part >= 10)
    {
        lcd_show_num(pos, y, integer_part, 2, 12, 0);
        pos += 12;
    }
    else
    {
        lcd_show_num(pos, y, integer_part, 1, 12, 0);
        pos += 6;
    }

    // 小数点和小数部分
    if(precision > 0 && pos < (LCD_WIDTH - 12))
    {
        lcd_show_char(pos, y, '.', 12, 0);
        pos += 6;

        // 计算小数部分（只显示1位小数）
        decimal_part = (unsigned int)((value - integer_part) * 10);
        if(decimal_part > 9) decimal_part = 9;

        lcd_show_char(pos, y, '0' + decimal_part, 12, 0);
    }
}

/*-----------------------------------------------------------------------------
 * 函数名称: Display_Time
 * 功能描述: 在指定位置显示时间
 * 输入参数: x,y - 坐标
 *----------------------------------------------------------------------------*/
void Display_Time(unsigned char x, unsigned char y)
{
    unsigned char pos = x;
    
    // 显示时:分:秒
    if(g_system.hour < 10)
    {
        lcd_show_char(pos, y, '0', 12, 0);
        pos += 6;
    }
    lcd_show_num(pos, y, g_system.hour, (g_system.hour >= 10) ? 2 : 1, 12, 0);
    pos += (g_system.hour >= 10) ? 12 : 6;
    
    lcd_show_char(pos, y, ':', 12, 0);
    pos += 6;
    
    if(g_system.minute < 10)
    {
        lcd_show_char(pos, y, '0', 12, 0);
        pos += 6;
    }
    lcd_show_num(pos, y, g_system.minute, (g_system.minute >= 10) ? 2 : 1, 12, 0);
    pos += (g_system.minute >= 10) ? 12 : 6;
    
    lcd_show_char(pos, y, ':', 12, 0);
    pos += 6;
    
    if(g_system.second < 10)
    {
        lcd_show_char(pos, y, '0', 12, 0);
        pos += 6;
    }
    lcd_show_num(pos, y, g_system.second, (g_system.second >= 10) ? 2 : 1, 12, 0);
}

/*-----------------------------------------------------------------------------
 * 函数名称: Display_Main_Screen
 * 功能描述: 主界面显示
 *----------------------------------------------------------------------------*/
void Display_Main_Screen(void)
{
    // 清除显示缓存，避免残留字符
    lcd9648_clear();
    
    // 第1行：温度信息
    lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "T:");
    Display_Float(12, 0, g_system.current_temp, 1);
    lcd_show_string(42, 0, LCD_WIDTH, 12, 12, "/");
    Display_Float(48, 0, g_system.target_temp, 1);
    lcd_show_string(78, 0, LCD_WIDTH, 12, 12, "C");
    
    // 第2行：电机状态
    lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "M:");
    Display_Float(12, 12, g_system.motor_voltage, 1);
    lcd_show_string(36, 12, LCD_WIDTH, 12, 12, "V S:");
    lcd_show_num(54, 12, g_system.motor_speed, 3, 12, 0);
    
    // 第3行：时钟显示
    lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "Time:");
    Display_Time(30, 24);
    
    // 第4行：系统状态（使用修正后的Display_Float）
    lcd_show_string(0, 36, LCD_WIDTH, 12, 12, "NTC:");
    Display_Float(24, 36, g_system.chip_temp, 1);
    lcd_show_string(42, 36, LCD_WIDTH, 12, 12, " I:");
    Display_Float(54, 36, g_system.current_detect, 1);
    lcd_show_string(78, 36, LCD_WIDTH, 12, 12, "A");
    
    // 刷新显示
    lcd_reflash_gram();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Display_Menu
 * 功能描述: 菜单界面显示
 *----------------------------------------------------------------------------*/
void Display_Menu(void)
{
    lcd9648_clear();
    
    switch(g_system.menu_index)
    {
        case MENU_TEMP_SET:
            lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "Set Temperature");
            lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "Target:");
            Display_Float(42, 12, g_system.target_temp, 1);
            lcd_show_string(72, 12, LCD_WIDTH, 12, 12, "C");
            lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "K1:+ K2:- K4:OK");
            break;
            
        case MENU_TIME_SET:
            lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "Set Time");
            lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "Time:");
            Display_Time(30, 12);
            lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "K1:+ K2:- K4:OK");
            break;
            
        case MENU_SYSTEM_INFO:
            lcd_show_string(0, 0, LCD_WIDTH, 12, 12, "System Info");
            lcd_show_string(0, 12, LCD_WIDTH, 12, 12, "Alarm Lv:");
            lcd_show_num(54, 12, g_system.alarm_level, 1, 12, 0);
            lcd_show_string(0, 24, LCD_WIDTH, 12, 12, "Motor St:");
            lcd_show_num(54, 24, g_system.motor_state, 1, 12, 0);
            break;
    }
    
    lcd_reflash_gram();
}

/*-----------------------------------------------------------------------------
 * 函数名称: Display_Update
 * 功能描述: 显示更新
 *----------------------------------------------------------------------------*/
void Display_Update(void)
{
    if(g_system.system_mode == MODE_NORMAL)
    {
        Display_Main_Screen();
    }
    else if(g_system.system_mode == MODE_SETTING)
    {
        Display_Menu();
    }
}
