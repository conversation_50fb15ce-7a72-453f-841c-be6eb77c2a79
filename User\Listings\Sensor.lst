C51 COMPILER V9.54   SENSOR                                                                07/07/2025 00:36:34 PAGE 1   


C51 COMPILER V9.54, COMPILATION OF MODULE SENSOR
OBJECT MODULE PLACED IN .\Objects\Sensor.obj
COMPILER INVOKED BY: E:\Keil\C51\BIN\C51.EXE ..\Driver\Sensor.c OPTIMIZE(8,SPEED) BROWSE INCDIR(..\Driver) DEBUG OBJECTE
                    -XTEND PRINT(.\Listings\Sensor.lst) OBJECT(.\Objects\Sensor.obj)

line level    source

   1          #include "Sensor.h"
   2          #include "ADC.h"
   3          
   4          /*=============================================================================
   5           * 传感器检测模块实现
   6           * 功能：NTC芯片温度检测、过流检测、多通道ADC采集
   7           *============================================================================*/
   8          
   9          /*-----------------------------------------------------------------------------
  10           * 函数名称: Sensor_Init
  11           * 功能描述: 传感器模块初始化
  12           *----------------------------------------------------------------------------*/
  13          void Sensor_Init(void)
  14          {
  15   1          // 初始化外部ADC芯片
  16   1          ADC_Init();
  17   1      }
  18          
  19          /*-----------------------------------------------------------------------------
  20           * 函数名称: Get_NTC_Temperature
  21           * 功能描述: 读取NTC热敏电阻芯片温度
  22           * 返回值: 芯片温度(℃)
  23           *----------------------------------------------------------------------------*/
  24          float Get_NTC_Temperature(void)
  25          {
  26   1          // 直接调用ADC驱动函数
  27   1          return ADC_Get_NTC_Temperature();
  28   1      }
  29          
  30          /*-----------------------------------------------------------------------------
  31           * 函数名称: Get_Current_Detect
  32           * 功能描述: 读取过流检测值
  33           * 返回值: 电流检测值(A)
  34           *----------------------------------------------------------------------------*/
  35          float Get_Current_Detect(void)
  36          {
  37   1          // 直接调用ADC驱动函数
  38   1          return ADC_Get_Current_Detect();
  39   1      }
  40          
  41          /*-----------------------------------------------------------------------------
  42           * 函数名称: Sensor_Update_All
  43           * 功能描述: 更新所有传感器数据
  44           *----------------------------------------------------------------------------*/
  45          void Sensor_Update_All(void)
  46          {
  47   1          // 更新NTC芯片温度
  48   1          Get_NTC_Temperature();
  49   1      
  50   1          // 更新过流检测值
  51   1          Get_Current_Detect();
  52   1      
  53   1          // 更新电机转速电压
  54   1          ADC_Get_Motor_Voltage();
C51 COMPILER V9.54   SENSOR                                                                07/07/2025 00:36:34 PAGE 2   

  55   1      }


MODULE INFORMATION:   STATIC OVERLAYABLE
   CODE SIZE        =     20    ----
   CONSTANT SIZE    =   ----    ----
   XDATA SIZE       =   ----    ----
   PDATA SIZE       =   ----    ----
   DATA SIZE        =   ----    ----
   IDATA SIZE       =   ----    ----
   BIT SIZE         =   ----    ----
END OF MODULE INFORMATION.


C51 COMPILATION COMPLETE.  0 WARNING(S),  0 ERROR(S)
