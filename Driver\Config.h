#ifndef __CONFIG_H__
#define __CONFIG_H__

#include <REG52.H>

/*=============================================================================
 * 系统配置文件 - 智能温控保温杯系统
 * 功能：统一管理引脚分配、系统参数、数据结构等
 *============================================================================*/

/*-----------------------------------------------------------------------------
 * 引脚分配定义 - 解决P1^4冲突问题
 *----------------------------------------------------------------------------*/
// 按键引脚（减少至4个）
sbit KEY1 = P1^0;  // 温度设定+
sbit KEY2 = P1^1;  // 温度设定-  
sbit KEY3 = P1^2;  // 菜单切换
sbit KEY4 = P1^3;  // 确认设置

// DS18B20温度传感器（保持原有）
sbit DS18B20_DQ = P1^4;  // 单总线数据线

// 电机控制引脚
sbit MOTOR_FWD = P1^6;   // 电机正转（制热）
sbit MOTOR_REV = P1^7;   // 电机反转（制冷）

// 报警输出引脚
sbit BUZZER = P1^5;      // 蜂鸣器控制
sbit LED1 = P0^0;        // LED指示灯1
sbit LED2 = P0^1;        // LED指示灯2

// DS1302时钟芯片引脚
sbit DS1302_CLK = P2^2;  // 时钟线
sbit DS1302_DAT = P2^3;  // 数据线
sbit DS1302_RST = P2^4;  // 复位线

// IIC通信引脚（保持原有）
sbit IIC_SCL = P2^0;     // IIC时钟线
sbit IIC_SDA = P2^1;     // IIC数据线

// ADC芯片SPI接口引脚
sbit ADC_DO = P3^2;      // ADC数据输出（MISO）
sbit ADC_DI = P3^3;      // ADC数据输入（MOSI）
sbit ADC_CS = P3^4;      // ADC片选信号
sbit ADC_CLK = P3^5;     // ADC时钟信号

// LCD9648显示引脚（保持原有）
sbit LCD_CS0 = P0^3;     // LCD片选
sbit LCD_RST = P0^2;     // LCD复位
sbit LCD_RS  = P0^4;     // LCD命令/数据选择
sbit LCD_SCL = P0^6;     // LCD时钟
sbit LCD_SDA = P0^5;     // LCD数据

/*-----------------------------------------------------------------------------
 * ADC通道分配
 *----------------------------------------------------------------------------*/
#define ADC_MOTOR_VOLTAGE   0x01  // 电机转速电压检测通道
#define ADC_NTC_TEMP        0x02  // NTC芯片温度检测通道  
#define ADC_CURRENT_DETECT  0x03  // 过流检测通道

/*-----------------------------------------------------------------------------
 * 系统参数定义
 *----------------------------------------------------------------------------*/
#define TEMP_MIN            20.0  // 最低设定温度(℃)
#define TEMP_MAX            90.0  // 最高设定温度(℃)
#define TEMP_DEFAULT        25.0  // 默认设定温度(℃)
#define TEMP_DEADZONE       1.0   // 温度控制死区(℃)

#define NTC_TEMP_MIN        25.0  // NTC温度下限(℃)
#define NTC_TEMP_MAX        85.0  // NTC温度上限(℃)

#define CURRENT_MAX         5.0   // 最大电流限制(A)
#define VOLTAGE_MAX         15.0  // 最大电压限制(V)

#define MOTOR_SPEED_MAX     255   // 电机最大转速(DAC值)
#define MOTOR_SPEED_MIN     0     // 电机最小转速

/*-----------------------------------------------------------------------------
 * ADC转换系数
 *----------------------------------------------------------------------------*/
#define ADC_VREF            5.0   // ADC参考电压(V)
#define ADC_RESOLUTION      256   // ADC分辨率(8位)

// 电机电压转换系数（根据分压电路调整）
#define MOTOR_VOLTAGE_K     (ADC_VREF / ADC_RESOLUTION * 3.0)

// NTC温度转换系数（根据NTC特性调整）
#define NTC_TEMP_K          0.5   // NTC温度转换系数
#define NTC_TEMP_OFFSET     25    // NTC温度偏移

// 电流检测转换系数（根据电流传感器调整）
#define CURRENT_K           (ADC_VREF / ADC_RESOLUTION * 2.0)

/*-----------------------------------------------------------------------------
 * 报警参数定义
 *----------------------------------------------------------------------------*/
#define BUZZER_BASE_FREQ    1000  // 蜂鸣器基础频率(Hz)
#define LED_BASE_PERIOD     500   // LED基础闪烁周期(ms)
#define ALARM_LEVEL_MAX     5     // 最大报警等级

/*-----------------------------------------------------------------------------
 * 串口通信参数
 *----------------------------------------------------------------------------*/
#define UART_BAUDRATE       9600  // 串口波特率
#define UART_SEND_INTERVAL  1000  // 串口发送间隔(ms)

/*-----------------------------------------------------------------------------
 * 系统数据结构定义
 *----------------------------------------------------------------------------*/
typedef struct {
    float target_temp;      // 设定目标温度(℃)
    float current_temp;     // DS18B20当前温度(℃)
    float chip_temp;        // NTC芯片温度(℃)
    float motor_voltage;    // 电机转速电压(V)
    float current_detect;   // 过流检测值(A)
    
    unsigned char motor_state;    // 电机状态：0停止，1正转，2反转
    unsigned char motor_speed;    // 电机转速(0-255)
    unsigned char alarm_level;    // 报警等级(0-5)
    
    unsigned char year;     // 年
    unsigned char month;    // 月
    unsigned char day;      // 日
    unsigned char hour;     // 时
    unsigned char minute;   // 分
    unsigned char second;   // 秒
    
    unsigned char system_mode;    // 系统模式：0正常，1设置
    unsigned char menu_index;     // 菜单索引
} SystemParams_t;

/*-----------------------------------------------------------------------------
 * 电机状态定义
 *----------------------------------------------------------------------------*/
#define MOTOR_STOP          0     // 电机停止
#define MOTOR_FORWARD       1     // 电机正转（制热）
#define MOTOR_REVERSE       2     // 电机反转（制冷）

/*-----------------------------------------------------------------------------
 * 系统模式定义
 *----------------------------------------------------------------------------*/
#define MODE_NORMAL         0     // 正常运行模式
#define MODE_SETTING        1     // 参数设置模式

/*-----------------------------------------------------------------------------
 * 菜单索引定义
 *----------------------------------------------------------------------------*/
#define MENU_TEMP_SET       0     // 温度设定菜单
#define MENU_TIME_SET       1     // 时间设定菜单
#define MENU_SYSTEM_INFO    2     // 系统信息菜单

/*-----------------------------------------------------------------------------
 * 全局变量声明
 *----------------------------------------------------------------------------*/
extern SystemParams_t g_system;  // 系统参数全局变量

/*-----------------------------------------------------------------------------
 * 全局变量定义（在main.c中定义）
 *----------------------------------------------------------------------------*/
#ifdef MAIN_C
SystemParams_t g_system = {
    TEMP_DEFAULT,  // target_temp
    25.0,          // current_temp
    25.0,          // chip_temp
    0.0,           // motor_voltage
    0.0,           // current_detect
    MOTOR_STOP,    // motor_state
    0,             // motor_speed
    0,             // alarm_level
    20,            // year
    1,             // month
    1,             // day
    12,            // hour
    0,             // minute
    0,             // second
    MODE_NORMAL,   // system_mode
    0              // menu_index
};
#endif

/*-----------------------------------------------------------------------------
 * 任务调度时间定义
 *----------------------------------------------------------------------------*/
#define TASK_1MS_PERIOD     1     // 1ms任务周期（按键扫描）
#define TASK_10MS_PERIOD    10    // 10ms任务周期（ADC采集）
#define TASK_100MS_PERIOD   100   // 100ms任务周期（温度读取、显示）
#define TASK_500MS_PERIOD   500   // 500ms任务周期（控制算法、报警）
#define TASK_1000MS_PERIOD  1000  // 1000ms任务周期（串口输出、时钟）

#endif // __CONFIG_H__
